# TODO

# Learn
- What are hooks and how to use them [Airtable hooks](https://airtable.com/developers/extensions/api/UI/hooks)
- [Using promises](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Using_promises)
- [Typescript syntax](https://ts.dev/style/#syntax)

## Route Handlers
- https://nextjs.org/docs/app/building-your-application/routing/route-handlers#convention
- https://developer.mozilla.org/en-US/docs/Web/HTTP/Methods

### Supported HTTP Methods
The following HTTP methods are supported: GET, POST, PUT, PATCH, DELETE, HEAD, and OPTIONS. If an unsupported method is called, Next.js will return a 405 Method Not Allowed response.

# Links
- [Read data from Airtable](https://airtable.com/developers/extensions/guides/read-data-from-airtable)
- [API Reference](https://airtable.com/developers/web/api/introduction)