import NextAuth from "next-auth"

import type {
    NextAuthConfig,
  } from "next-auth";

import EmailProvider from "next-auth/providers/email"
// import Apple from "next-auth/providers/apple"
// import Google from "next-auth/providers/google"
import { AirtableAdapter } from "./app/utils/airtable/adapter"
import { randomBytes, randomUUID } from "crypto"
// import { SessionUser } from "@/types/user";
import { AdapterUser } from "@auth/core/adapters";
// import { AirtableUser } from "@/types/airtable";


const adapter = AirtableAdapter()

export const authConfig : NextAuthConfig  = {
    adapter,
    providers: [
        // EmailProvider({
        //     server: {
        //         host: "smtp.resend.com",
        //         port: 587,
        //         username: "resend",
        //         auth: {
        //             user: "apikey",
        //             pass: process.env.AUTH_RESEND_KEY,
        //         }
        //     },
        //     from: process.env.AUTH_EMAIL_FROM,
        // })
        EmailProvider({
            server: {
              host: process.env.RESEND_HOST,
              port: process.env.RESEND_PORT,
              auth: {
                user: process.env.RESEND_USER,
                pass: process.env.RESEND_KEY,
              },
            },
            from: process.env.EMAIL_FROM,
          }),
    ],
    pages: {
        signIn: '/auth/signin',
        signOut: '/auth/signout',
        error: '/auth/error',
        verifyRequest: '/auth/verify-request',
    },
    session: {
        strategy: "database",
        maxAge: 30 * 24 * 60 * 60, // 30 days
        updateAge: 24 * 60 * 60, // 24 hours
        generateSessionToken: () => {
        return randomUUID?.() ?? randomBytes(32).toString("hex")
        },
   },
    callbacks: {
        async signIn({ user, email, account }) {
            try {
                // If this is a verification request, just allow it
                if (email?.verificationRequest) {
                    return true;
                }

                // Ensure user and email exist
                if (!user?.email) {
                    console.error("No user email provided");
                    return false;
                }

                // Check if user exists
                let dbUser = await adapter.getUserByEmail!(user.email);

                // If user doesn't exist, create them
                if (!dbUser) {
                    try {
                        dbUser = await adapter.createUser!({
                            email: user.email,
                            emailVerified: null,
                            name: null,
                            image: null,
                        } as AdapterUser ); 
                    } catch (createError) {
                        console.error("Error creating user:", createError);
                        return false;
                    }
                }

                // If this is an email sign-in and we have a user, mark them as verified
                if (account?.type === "email" && dbUser) {
                    try {
                        await adapter.updateUser!({
                            ...dbUser,
                            emailVerified: new Date(),
                        });
                    } catch (updateError) {
                        console.error("Error updating user verification:", updateError);
                        return false;
                    }
                }

                return true;
            } catch (error) {
                console.error("Error in signIn callback:", error);
                return false;
            }
        },
        async session({ session, user }) {
            // Use type assertion to access custom properties
            const extendedUser = user as typeof user & { airtableId?: string; customId?: string };

            if (session.user) {
                // Use the Airtable record ID (which starts with 'rec')
                session.user.id = user.id; // This should now be the Airtable record ID
                session.user.email = user.email;

                // Add additional fields for debugging
                if (extendedUser.airtableId) {
                    (session.user).airtableId = extendedUser.airtableId;
                }
                if (extendedUser.customId) {
                    (session.user).customId = extendedUser.customId;
                }
            } else {
            }
            return session;
        },
    },
    debug: false,
}

export const { handlers, auth, signIn, signOut } = NextAuth(authConfig)
