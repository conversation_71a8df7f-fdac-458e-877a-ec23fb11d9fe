import { base } from '@/app/utils/airtable';
import type { AdapterUser } from '@auth/core/adapters';
import { User } from '@/types/user';

function formatAirtableDate(date: Date): string {
    return date.toISOString().replace('T', ' ').substring(0, 19);
}

export async function createUser(data: Partial<AdapterUser>) {
    try {
        const records = await base('Users').create([{
            fields: {
                id: `user_${Date.now()}`,
                email: data.email,
                emailVerified: data.emailVerified ? formatAirtableDate(data.emailVerified) : null,
                name: data.name || null,
                image: data.image || null,
            }
        }]);
        const record = records[0];
        return {
            id: record.id, // Use Airtable record ID instead of custom ID
            airtableId: record.id, // Store the Airtable record ID separately
            customId: record.get('id') as string, // Keep the original ID for backward compatibility
            email: record.get('email') as string,
            emailVerified: record.get('emailVerified') ? new Date(record.get('emailVerified') as string) : null,
            name: record.get('name') as string | null,
            image: record.get('image') as string | null,
        };
    } catch (error) {
        console.error('Error creating user:', error);
        throw error;
    }
}

export async function getUser(id: string): Promise<User | null> {
    try {
        let records;

        // Check if the ID is an Airtable record ID (starts with 'rec')
        if (id.startsWith('rec')) {
            // Directly fetch by Airtable record ID
            try {
                const record = await base('Users').find(id);
                records = [record];
            } catch (error) {
                console.error(`Failed to find user by Airtable record ID: ${error instanceof Error ? error.message : String(error)}`);
                // Fall back to searching by custom ID
                records = await base('Users').select({ 
                    filterByFormula: `{id} = '${id.replace(/'/g, "\\'")}'` 
                }).firstPage();
            }
        } else {
            // Try to find by custom ID field
            records = await base('Users').select({ filterByFormula: `{id} = '${id}'` }).firstPage();
        }

        if (!records.length) return null;

        const record = records[0];
        const user: User = {
            id: record.id, // Use Airtable record ID instead of custom ID
            airtableId: record.id, // Store the Airtable record ID separately
            customId: record.get('id') as string, // Keep the original ID for backward compatibility
            email: record.get('email') as string,
            emailVerified: record.get('emailVerified') ? new Date(record.get('emailVerified') as string) : null,
            name: record.get('name') as string | null,
            image: record.get('image') as string | null,
        };
        return user;
    } catch (error) {
        console.error('Error in getUser:', error);
        return null;
    }
}

export async function getUserByEmail(email: string) {
    try {
        const records = await base('Users').select({
            filterByFormula: `{email} = '${email}'`
        }).firstPage();

        if (records.length === 0) {
            return null;
        }

        const record = records[0];
        return {
            id: record.id,
            email: record.fields.email,
        };
    } catch (error) {
        console.error('Error fetching user:', error);
        throw error;
    }
}

export async function getUserById(userId: string) {
    try {
        const record = await base('Users').find(userId);
        return {
            id: record.id,
            email: record.fields.email,
        };
    } catch (error) {
        console.error('Error fetching user:', error);
        throw error;
    }
}

export async function updateUser(data: Partial<AdapterUser> & { id: string }) {
    try {
        // First try to find by ID
        const records = await base('Users').select({
            filterByFormula: `OR(RECORD_ID() = '${data.id}', {email} = '${data.email}')`
        }).firstPage();

        if (!records.length) {
            console.error('User not found for update:', data);
            throw new Error('User not found');
        }

        const record = records[0];
        const fields: Record<string, string> = {};

        if (data.email) fields.email = data.email;
        if (data.emailVerified instanceof Date) {
            fields.emailVerified = data.emailVerified.toISOString();
        }
        if (data.name) fields.name = data.name;
        if (data.image) fields.image = data.image;

        const updatedRecords = await base('Users').update([
            {
                id: record.id,
                fields
            }
        ]);

        const updatedRecord = updatedRecords[0];
        return {
            id: updatedRecord.id,
            email: updatedRecord.fields.email as string,
            emailVerified: updatedRecord.fields.emailVerified
                ? new Date(updatedRecord.fields.emailVerified as string)
                : null,
            name: updatedRecord.fields.name as string || null,
            image: updatedRecord.fields.image as string || null
        };
    } catch (error) {
        console.error('Error updating user:', error);
        throw error;
    }
}

export async function deleteUser(userId: string) {
    const records = await base('Users').select({ filterByFormula: `{id} = '${userId}'` }).firstPage();
    if (records.length) await base('Users').destroy(records[0].id);
}
