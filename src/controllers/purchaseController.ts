'use server';
import { base } from '@/app/utils/airtable';
import { cache } from 'react';

export async function getUserPurchases(userId: string, userEmail: string) {
    try {
        const records = await base('Purchases').select({
            filterByFormula: `{email} = '${userEmail}'`
        }).firstPage();

        return records.map(record => ({
            id: record.id,
            clientId: record.fields.clientId,
            clientEmail: record.fields.clientEmail,
            productName: record.fields.productName,
            productId: record.fields.productId,
            purchaseDate: record.fields.purchaseDate ? new Date(record.fields.purchaseDate as string) : null,
        }));
    } catch (error) {
        console.error('Error fetching purchases:', error);
        throw error;
    }
}

export async function getUserPurchase(userId: string, productId: string) {
    try {
        const records = await base('Purchases').select({
            filterByFormula: `AND({clientId} = '${userId}', {productId} = '${productId}')`
        }).firstPage();

        const purchases = records.map(record => ({
            id: record.id,
            clientEmail: record.fields.email,
            clientId: record.fields.clientId,
            productName: record.fields.productName,
            productId: record.fields.productId,
            purchaseDate: record.fields.purchaseDate ? new Date(record.fields.purchaseDate as string) : null,
        }));

        return purchases;
    } catch (error) {
        console.error('Error fetching purchases:', error);
        throw error;
    }
}

export const getUserPurchaseStatus = cache(async (userId: string, productId: string) => {
    if (!userId || !productId) {
      return { hasPurchased: false };
    }
    
    try {
      const purchases = await getUserPurchase(userId, productId);
      return { 
        hasPurchased: purchases.length > 0,
        purchases
      };
    } catch (error) {
      console.error('Error fetching purchase status:', error);
      return { hasPurchased: false, error };
    }
  });

export async function createPurchase(userId: string, userEmail: string, productName: string, productId?: string) {
    try {
        const records = await base('Purchases').create([
            {
                fields: {
                    clientId: userId,
                    email: userEmail,
                    clientEmail: userEmail,
                    productName: productName,
                    productId: productId,
                    purchaseDate: new Date().toISOString().split('T')[0],
                }
            }
        ]);

        return {
            id: records[0].id,
            email: records[0].fields.email,
            productName: records[0].fields.productName,
            productId: records[0].fields.productId,
            purchaseDate: records[0].fields.purchaseDate
        };
    } catch (error) {
        console.error('Error creating purchase:', error);
        throw error;
    }
}

export async function deletePurchase(userId: string, userEmail: string, productId: string) {
    try {
        const purchaseRecord = await base('Purchases').find(productId);
        const purchaseEmail = purchaseRecord.fields.email;

        if (purchaseEmail !== userEmail) {
            throw new Error('Purchase does not belong to this user');
        }

        await base('Purchases').destroy(productId);
        return true;
    } catch (error) {
        console.error('Error deleting purchase:', error);
        throw error;
    }
}