import { cache } from 'react';
import { base } from "@/lib/airtable";
import { AirtableProduct } from "@/types/airtable";

// export async function getDBproducts(): Promise<AirtableProduct[]> {

export const getDBproducts = cache(async (): Promise<AirtableProduct[]> => {
    try {
        const records = await base('Products').select().firstPage();
        return records.map(record => ({
            id: record.id,
            name: record.fields.name as string,
            price: record.fields.price as number,
            priceOld: record.fields.priceOld as number,
            type: record.fields.type as string,
            slug: (record.fields.name as string).toLowerCase().replace(/\s+/g, '-')
        }));
    } catch (error) {
        console.error('Error getting products:', error);
        throw error;
        // return [];
    }
})

export async function getDBproductById(productId: string): Promise<AirtableProduct> {
    try {
        // We can use getDBproducts() here since we're dealing with a small dataset
        // This avoids potential Airtable rate limiting issues
        const products = await getDBproducts();
        const product = products.find(p => p.id === productId);
        return product;
    } catch (error) {
        console.error('Error getting product by id:', error);
        throw error;
    }
}

export async function getDBproductBySlug(slug: string): Promise<AirtableProduct> {
    try {
        const products = await getDBproducts();
        const product = products.find(p => p.slug === slug);
        return product || null;
    } catch (error) {
        console.error('Error getting product by slug:', error);
        throw error;
    }
}
