// 'use client';

// import { useState, Suspense } from 'react';
// import { signIn } from 'next-auth/react';
// import { useSearchParams } from 'next/navigation';

// export default function SignInErrorPage() {
//   const [email, setEmail] = useState('');
//   const [message, setMessage] = useState<string | null>(null);
//   const searchParams = useSearchParams();
//   const callbackUrl = searchParams.get('callbackUrl') || '/'; // Default to '/' if not provided

//   const handleSubmit = async (e: React.FormEvent) => {
//     e.preventDefault();
//     setMessage(null);
//     const res = await signIn('email', { email, callbackUrl, redirect: false });
//     // console.log('signIn response:', res);
//     if (res?.error) {
//       setMessage(`Error: ${res.error}`);
//     } else if (res?.ok) {
//       setMessage('Check your email for a magic link! It may take a moment to arrive—be sure to check your spam folder if you don’t see it.');
//     } else {
//       setMessage('Unexpected response. Please try again.');
//     }
//   };

//   return (
//     <div className="flex min-h-screen items-center justify-center bg-gray-100">
//       <div className="w-full max-w-md p-6 bg-white rounded-lg shadow-md">
//         <h1 className="text-2xl font-bold mb-4">Sign In</h1>
//         <Suspense fallback={<div>Loading form...</div>}>
//           <form onSubmit={handleSubmit}>
//             <div className="mb-4">
//               <label htmlFor="email" className="block text-sm font-medium text-gray-700">Email</label>
//               <input
//                 type="email"
//                 id="email"
//                 value={email}
//                 onChange={(e) => setEmail(e.target.value)}
//                 className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
//                 placeholder="<EMAIL>"
//                 required
//               />
//             </div>
//             <button
//               type="submit"
//               className="w-full px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
//             >
//               Send Magic Link
//             </button>
//           </form>
//         </Suspense>

//         {message && (
//           <div className="mt-4 text-center text-sm text-gray-600">
//             <p>{message}</p>
//             <p className="mt-2">Once you click the link in your email, you’ll be signed in and redirected back to your original page.</p>
//           </div>
//         )}
//       </div>
//     </div>
//   );
// }

'use client';

import { useState, Suspense } from 'react';
import { signIn } from 'next-auth/react';
import { useSearchParams } from 'next/navigation';

function SignInForm() {
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState<string | null>(null);
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl') || '/'; // Default to '/' if not provided

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setMessage(null);
    const res = await signIn('email', { email, callbackUrl, redirect: false });
    // console.log('signIn response:', res);
    if (res?.error) {
      setMessage(`Error: ${res.error}`);
    } else if (res?.ok) {
      setMessage('Check your email for a magic link! It may take a moment to arrive—be sure to check your spam folder if you dont see it.');
    } else {
      setMessage('Unexpected response. Please try again.');
    }
  };

  return (
    <div className="w-full max-w-md p-6 bg-white rounded-lg shadow-md">
      <h1 className="text-2xl font-bold mb-4">Sign In</h1>
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label htmlFor="email" className="block text-sm font-medium text-gray-700">Email</label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="<EMAIL>"
            required
          />
        </div>
        <button
          type="submit"
          className="w-full px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
        >
          Send Magic Link
        </button>
      </form>

      {message && (
        <div className="mt-4 text-center text-sm text-gray-600">
          <p>{message}</p>
          <p className="mt-2">Once you click the link in your email, you&apos;ll be signed in and redirected back to your original page.</p>
        </div>
      )}
    </div>
  );
}

export default function SignInErrorPage() {
  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-100">
      <Suspense fallback={<div>Loading...</div>}>
        <SignInForm />
      </Suspense>
    </div>
  );
}