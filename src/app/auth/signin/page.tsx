'use client';

import { useState, Suspense } from 'react';
import { signIn } from 'next-auth/react';
import { useSearchParams } from 'next/navigation';

function SignInForm() {
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState<string | null>(null);
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl') || '/';

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setMessage(null);

    try {
      const res = await signIn('email', {
        email,
        redirect: false,
        callbackUrl,
      });

      if (res?.error) {
        setMessage(`Error: ${res.error}`);
      } else if (res?.ok) {
        setMessage(
          'Check your email for a magic link! It may take a moment to arrive—be sure to check your spam folder if you don\'t see it.'
        );
      } else {
        setMessage('Unexpected response. Please try again.');
      }
    } catch (error) {
      console.error('Sign in error:', error);
      setMessage('An error occurred during sign in. Please try again.');
    }
  };

  return (
    <>
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label htmlFor="email" className="block text-sm font-medium text-gray-700">
            Email
          </label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="<EMAIL>"
            required
          />
        </div>
        <button
          type="submit"
          className="w-full px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
        >
          Send Magic Link
        </button>
      </form>

      {message && (
        <div className="mt-4 text-center text-sm text-gray-600">
          <p>{message}</p>
          <p className="mt-2">
            Once you click the link in your email, you will be signed in and redirected back to your original page.
          </p>
        </div>
      )}
    </>
  );
}

// export default function SignInPage() {
//   return (
//     <div className="flex min-h-screen items-center justify-center bg-gray-100">
//       <div className="w-full max-w-md p-6 bg-white rounded-lg shadow-md">
//         <h1 className="text-2xl font-bold mb-4">Sign In</h1>
//         {/* ✅ Wrap component that uses useSearchParams in Suspense */}
//         <Suspense fallback={<div>Loading form...</div>}>
//           <SignInForm />
//         </Suspense>
//       </div>
//     </div>
//   );
// }


// function SignInForm() {
//   const [email, setEmail] = useState('');
//   const [message, setMessage] = useState<string | null>(null);
//   const searchParams = useSearchParams();
//   const callbackUrl = searchParams.get('callbackUrl') || '/';

//   const handleSubmit = async (e: React.FormEvent) => {
//     e.preventDefault();
//     setMessage(null);

//     try {
//       const res = await signIn('email', {
//         email,
//         redirect: false,
//         callbackUrl,
//       });

//       if (res?.error) {
//         setMessage(`Error: ${res.error}`);
//       } else if (res?.ok) {
//         setMessage(
//           'Check your email for a magic link! It may take a moment to arrive—be sure to check your spam folder if you don\'t see it.'
//         );
//       } else {
//         setMessage('Unexpected response. Please try again.');
//       }
//     } catch (error) {
//       console.error('Sign in error:', error);
//       setMessage('An error occurred during sign in. Please try again.');
//     }
//   };

//   return (
//     <>
//       <form onSubmit={handleSubmit} className="space-y-4">
//         <div>
//           <label htmlFor="email" className="block text-sm font-medium text-gray-700">
//             Email
//           </label>
//           <input
//             type="email"
//             id="email"
//             value={email}
//             onChange={(e) => setEmail(e.target.value)}
//             className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
//             placeholder="<EMAIL>"
//             required
//           />
//         </div>

//         <button
//           type="submit"
//           className="w-full px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
//         >
//           Send Magic Link
//         </button>
//       </form>

//       <hr className="my-6 border-gray-300" />

//       {/* Google sign-in button */}
//       <button
//         onClick={() => signIn('google', { callbackUrl })}
//         className="w-full px-4 py-2 flex items-center justify-center bg-white border border-gray-300 text-gray-800 rounded-md hover:bg-gray-100"
//       >
//         <svg
//           className="w-5 h-5 mr-2"
//           viewBox="0 0 48 48"
//           xmlns="http://www.w3.org/2000/svg"
//         >
//           <path
//             fill="#EA4335"
//             d="M24 9.5c3.93 0 7.46 1.42 10.24 4.2l6.92-6.92C36.58 2.62 30.73 0 24 0 14.73 0 6.7 5.24 2.78 13.06l7.97 6.19C12.61 13.62 17.91 9.5 24 9.5z"
//           />
//           <path
//             fill="#FBBC04"
//             d="M46.41 24c0-1.64-.15-3.24-.41-4.78H24v9.04h12.58c-.54 2.92-2.17 5.41-4.59 7.1l7.01 5.45C43.34 37.01 46.41 31.14 46.41 24z"
//           />
//           <path
//             fill="#34A853"
//             d="M9.7 28.26c-1.08-3.17-1.08-6.35 0-9.52l-7.97-6.19c-2.93 5.89-2.93 12.91 0 18.8l7.97-6.19z"
//           />
//           <path
//             fill="#4285F4"
//             d="M24 46.41c6.73 0 12.58-2.62 16.73-7.17l-7.01-5.45c-2.02 1.37-4.58 2.17-7.32 2.17-5.62 0-10.37-3.63-12.08-8.54l-7.97 6.19C6.7 42.76 14.73 48 24 48z"
//           />
//         </svg>
//         Continue with Google
//       </button>

//       {message && (
//         <div className="mt-4 text-center text-sm text-gray-600">
//           <p>{message}</p>
//           <p className="mt-2">
//             Once you click the link in your email, you will be signed in and redirected back to your original page.
//           </p>
//         </div>
//       )}
//     </>
//   );
// }

export default function SignInPage() {
  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-100">
      <div className="w-full max-w-md p-6 bg-white rounded-lg shadow-md">
        <h1 className="text-2xl font-bold mb-4">Sign In</h1>
        {/* ✅ Wrap component that uses useSearchParams in Suspense */}
        <Suspense fallback={<div>Loading form...</div>}>
          <SignInForm />
        </Suspense>
      </div>
    </div>
  );
}
