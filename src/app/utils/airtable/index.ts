import Airtable from 'airtable';
// import type { Adapter } from '@auth/core/adapters';
// import * as userMethods from './user';
// import * as sessionMethods from './session';
// import * as verificationTokenMethods from './verification-token';

if (!process.env.AIRTABLE_API_KEY) {
    throw new Error('AIRTABLE_API_KEY is not defined');
}

if (!process.env.AIRTABLE_BASE_ID) {
    throw new Error('AIRTABLE_BASE_ID is not defined');
}

export const base = new Airtable({ apiKey: process.env.AIRTABLE_API_KEY }).base(process.env.AIRTABLE_BASE_ID);
// const purchasesTable = await base('Purchases');
// const productsTable = await base('Products');

// export function AirtableAdapter(): Adapter {
//     return {
//         createUser: userMethods.createUser,
//         getUser: userMethods.getUser,
//         getUserByEmail: userMethods.getUserByEmail,
//         updateUser: userMethods.updateUser,
//         deleteUser: userMethods.deleteUser,

//         createSession: sessionMethods.createSession,
//         getSessionAndUser: async (sessionToken) => {
//             const session = await sessionMethods.getSession(sessionToken);
//             if (!session) return null;

//             // Try to get user by userId (which could be either custom ID or Airtable record ID)
//             const user = await userMethods.getUser(session.userId);
//             if (!user) return null;

//             // Ensure session uses the Airtable record ID
//             session.userId = user.id;

//             return { session, user };
//         },
//         updateSession: sessionMethods.updateSession,
//         deleteSession: sessionMethods.deleteSession,

//         createVerificationToken: verificationTokenMethods.createVerificationToken,
//         useVerificationToken: verificationTokenMethods.useVerificationToken,
//     };
// }

// export async function getUserPurchases(email: string) {
//     try {
//         const records = await purchasesTable.select({
//             filterByFormula: `{email} = '${email}'`,
//         }).firstPage();

//         return records.map(record => ({
//             id: record.id,
//             email: record.fields.email as string,
//             productName: record.fields.productName as string,
//             purchaseDate: new Date(record.fields.purchaseDate as string),
//         }));
//     } catch (error) {
//         console.error('Error getting user purchases:', error);
//         return [];
//     }
// }