import Airtable from 'airtable';
import type { VerificationToken } from '@auth/core/adapters';

const base = new Airtable({ apiKey: process.env.AIRTABLE_API_KEY }).base(process.env.AIRTABLE_BASE_ID || '');

function formatAirtableDate(date: Date): string {
    return date.toISOString().replace('T', ' ').substring(0, 19);
}

export async function createVerificationToken(data: VerificationToken) {
    try {
        const records = await base('VerificationTokens').create([{
            fields: {
                identifier: data.identifier,
                token: data.token,
                expires: formatAirtableDate(data.expires),
            },
        }] );

        const record = records[0];
        return {
            identifier: record.get('identifier') as string,
            token: record.get('token') as string,
            expires: new Date(record.get('expires') as string),
        };
    } catch (error) {
        console.error('Error creating verification token:', error);
        throw error;
    }
}

export async function useVerificationToken(data: { identifier: string; token: string }) {
    const records = await base('VerificationTokens').select({
        filterByFormula: `AND({identifier} = '${data.identifier}', {token} = '${data.token}')`,
    }).firstPage();
    if (!records.length) return null;
    const record = records[0];
    await base('VerificationTokens').destroy(record.id);
    return {
        identifier: record.get('identifier') as string,
        token: record.get('token') as string,
        expires: new Date(record.get('expires') as string),
    };
} 