// src/app/utils/airtable/adapter.ts
// **************************************************
// Exported from this file is used to configure NextAuth.js in your application. 
// It provides the necessary methods for NextAuth to interact with your Airtable database for authentication purposes, 
//
// including:
// - User management (create, get, update, delete)
// - Session management
// - Verification token handling
//
// This adapter is a critical part of your authentication system, allowing users to sign in with email and maintaining 
// their sessions in your Airtable database.
// **************************************************

import type { Adapter } from '@auth/core/adapters';
import * as userMethods from '@/controllers/userController';
import * as sessionMethods from './session';
import * as verificationTokenMethods from './verification-token';

// const base = new Airtable({ apiKey: process.env.AIRTABLE_API_KEY }).base(process.env.AIRTABLE_BASE_ID || '');

// function formatAirtableDate(date: Date): string {
//     return date.toISOString().replace('T', ' ').substring(0, 19);
// }

export function AirtableAdapter(): Adapter {
    return {
        createUser: userMethods.createUser,
        getUser: userMethods.getUser as Adapter['getUser'],
        // getUserByEmail: userMethods.getUserByEmail,
        getUserByEmail: async (email) => {
            const user = await userMethods.getUserByEmail(email);
          
            if (!user) return null;
          
            // Wrap returned object to match AdapterUser shape
            return {
              id: user.id,
              email: typeof user.email === 'string' ? user.email : String(user.email),
              emailVerified: null, // Airtable might not support this, so default to null
              name: null,
              image: null,
            };
          },
        updateUser: userMethods.updateUser as Adapter['updateUser'],
        deleteUser: userMethods.deleteUser as Adapter['deleteUser'],
        createSession: sessionMethods.createSession,
        // getSessionAndUser: async (sessionToken) => {
        //     const session = await sessionMethods.getSession(sessionToken);
        //     if (!session) return null;
        //     const user = await userMethods.getUser(session.userId);
        //     if (!user) return null;
        //     return { session, user };
        // },
        // updateSession: sessionMethods.updateSession,
        getSessionAndUser: async (sessionToken) => {
            const session = await sessionMethods.getSession(sessionToken);
            if (!session) return null;
          
            const user = await userMethods.getUser(session.userId);
            if (!user) return null;
          
            // Return values shaped to match AdapterSession and AdapterUser
            return {
              session: {
                id: session.id,
                sessionToken: session.sessionToken,
                userId: session.userId,
                expires: session.expires,
              },
              user: {
                id: user.id,
                email: typeof user.email === 'string' ? user.email : String(user.email),
                emailVerified: user.emailVerified ?? null,
                name: user.name ?? null,
                image: user.image ?? null,
              },
            };
          },
        updateSession: sessionMethods.updateSession,
        deleteSession: sessionMethods.deleteSession,
        createVerificationToken: verificationTokenMethods.createVerificationToken,
        useVerificationToken: verificationTokenMethods.useVerificationToken,
    };
}