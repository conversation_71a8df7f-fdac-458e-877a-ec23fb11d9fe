import { AdapterSession } from "@auth/core/adapters";
import { base } from "@/lib/airtable";

const table = await base('Sessions');

export async function createSession(data: AdapterSession) {
    try {
        const records = await table.create([
            {
                fields: {
                    sessionToken: data.sessionToken,
                    userId: data.userId,
                    expires: data.expires.toISOString(),
                }
            }
        ]);

        const record = records[0];
        return {
            id: record.id, // Include Airtable record ID
            sessionToken: record.fields.sessionToken as string,
            userId: record.fields.userId as string,
            expires: new Date(record.fields.expires as string),
        };
    } catch (error) {
        console.error('Airtable createSession error:', error);
        throw error;
    }
}

export async function getSession(sessionToken: string) {
    // console.log('getSession called with sessionToken:', sessionToken);
    try {
        const records = await table.select({
            filterByFormula: `{sessionToken} = '${sessionToken}'`,
            maxRecords: 1
        }).firstPage();

        // console.log('getSession found records:', records ? records.length : 0);
        if (!records || records.length === 0) {
            return null;
        }

        const record = records[0];
        const session = {
            id: record.id, // Include Airtable record ID
            sessionToken: record.fields.sessionToken as string,
            userId: record.fields.userId as string,
            expires: new Date(record.fields.expires as string),
        };
        // console.log('getSession returning session with userId:', session.userId);
        return session;
    } catch (error) {
        console.error('Airtable getSession error:', error);
        return null;
    }
}

export async function updateSession(data: Partial<AdapterSession> & { sessionToken: string }) {
    try {
        const records = await table.select({
            filterByFormula: `{sessionToken} = '${data.sessionToken}'`,
            maxRecords: 1
        }).firstPage();

        if (!records || records.length === 0) return null;
        const record = records[0];

        const updatedRecords = await table.update([
            {
                id: record.id,
                fields: {
                    sessionToken: data.sessionToken,
                    userId: data.userId,
                    expires: data.expires?.toISOString(),
                },
            },
        ]);

        const updatedRecord = updatedRecords[0];
        return {
            id: updatedRecord.id, // Include Airtable record ID
            sessionToken: updatedRecord.fields.sessionToken as string,
            userId: updatedRecord.fields.userId as string,
            expires: new Date(updatedRecord.fields.expires as string),
        };
    } catch (error) {
        console.error('Airtable updateSession error:', error);
        return null;
    }
}

export async function deleteSession(sessionToken: string) {
    try {
        const records = await table.select({
            filterByFormula: `{sessionToken} = '${sessionToken}'`,
            maxRecords: 1
        }).firstPage();

        if (!records || records.length === 0) return;
        await table.destroy([records[0].id]);
    } catch (error) {
        console.error('Airtable deleteSession error:', error);
        throw error;
    }
}