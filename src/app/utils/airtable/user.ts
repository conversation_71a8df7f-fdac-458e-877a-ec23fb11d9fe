import Airtable from 'airtable';
import { User } from '@/types/user';

const base = new Airtable({ apiKey: process.env.AIRTABLE_API_KEY }).base(process.env.AIRTABLE_BASE_ID || '');

export async function getUserByEmail(email: string): Promise<User | null> {
    // console.log('getUserByEmail called with email:', email);
    try {
        const records = await base('Users').select({ filterByFormula: `{email} = '${email}'` }).firstPage();
        // console.log('getUserByEmail found records:', records.length);
        if (!records.length) return null;
        const record = records[0];
        const user: User = {
            id: record.id, // Use Airtable record ID instead of custom ID
            airtableId: record.id, // Store the Airtable record ID separately
            customId: record.get('id') as string, // Keep the original ID for backward compatibility
            email: record.get('email') as string,
            emailVerified: record.get('emailVerified') ? new Date(record.get('emailVerified') as string) : null,
            name: record.get('name') as string | null,
            image: record.get('image') as string | null,
        };
        // console.log('getUserByEmail returning user:', user);
        return user;
    } catch (error) {
        console.error('Error in getUserByEmail:', error);
        return null;
    }
}


