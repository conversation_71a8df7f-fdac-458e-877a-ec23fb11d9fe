// // // src/app/utils/authoptions.ts
// import NextAuth, { AuthOptions, Session, DefaultSession } from "next-auth";
// // import { JWT } from "next-auth/jwt";
// import EmailProvider from "next-auth/providers/email";
// import { AirtableAdapter } from "./airtable/adapter";
// import { User as UserType } from '@/types/user';

// // Extend the built-in session type
// declare module "next-auth" {
//     interface Session {
//         user: {
//             id?: string;
//         } & DefaultSession["user"]
//     }
// }

// const adapter = AirtableAdapter();

// if (!adapter.createUser || !adapter.getUserByEmail) {
//     throw new Error("Required adapter methods are not implemented");
// }

// export const authOptions: AuthOptions = {
//     session: {
//         strategy: "database",
//         maxAge: 30 * 24 * 60 * 60, // 30 days
//     },
//     providers: [
//         EmailProvider({
//             server: {
//                 host: "smtp.sendgrid.net",
//                 port: 587,
//                 auth: {
//                     user: "apikey",
//                     pass: process.env.AUTH_SENDGRID_KEY,
//                 },
//             },
//             from: process.env.AUTH_EMAIL_FROM,
//             maxAge: 24 * 60 * 60, // 24 hours
//         }),
//     ],
//     adapter,
//     pages: {
//         signIn: '/auth/signin',
//         signOut: '/auth/signout',
//         error: '/auth/error',
//         verifyRequest: '/auth/verify-request',
//     },
//     callbacks: {
//         async signIn({ user, email, account }) {
//             try {
//                 // If this is a verification request, just allow it
//                 if (email?.verificationRequest) {
//                     return true;
//                 }

//                 // Check if user exists
//                 let dbUser = await adapter.getUserByEmail!(user.email!);

//                 // If user doesn't exist, create them
//                 if (!dbUser) {
//                     dbUser = await adapter.createUser!({
//                         id: `user_${Date.now()}`,
//                         email: user.email!,
//                         emailVerified: null,
//                         name: null,
//                         image: null,
//                     });
//                 }

//                 // If this is an email sign-in and we have a user, mark them as verified
//                 if (account?.type === "email" && dbUser) {
//                     await adapter.updateUser!({
//                         ...dbUser,
//                         emailVerified: new Date(),
//                     });
//                 }

//                 return true;
//             } catch (error) {
//                 console.error("Error in signIn callback:", error);
//                 return false;
//             }
//         },
//         async session({ session, user }: { session: Session, user: UserType }) {
//             if (session.user) {
//                 session.user.id = user.id;
//                 session.user.email = user.email;
//             }
//             return session;
//         },
//     },
//     debug: false,
//     secret: process.env.NEXTAUTH_SECRET || "your-default-secret-do-not-use-in-production",
// };

// export default NextAuth(authOptions);
