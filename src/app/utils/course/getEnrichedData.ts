import slugify from "@/app/utils/slugify";
import {
    Course,
    Section,
    Lesson
} from "@/types/course";

// Returns an array of courses
export default function getEnrichedData(dataCourseContent: Course): Course[] {

    // console.log('getEnrichedData init')

    const data = Array.isArray(dataCourseContent) ? dataCourseContent : [dataCourseContent];

    if (!data) {
        console.error("dataCourseContent is undefined or not an array", data);
    }

    return data.map((course) => {
        let lessonCounter = 0;
        const courseSlug = slugify(course.name)
        const courseName = course.name

        const enrichedCourse: Course = {
            ...course,
            courseSlug,
            courseName,
            'section': course.section && course.section.map((section: Section) => {
                const sectionSlug = slugify(section.name)
                return {
                    ...section,
                    sectionSlug,
                    courseSlug,
                    'lesson': section.lesson && section.lesson.map((lesson: Lesson) => {
                        const lessonSlug = slugify(lesson.name)
                        return {
                            ...lesson,
                            courseName,
                            courseSlug,
                            sectionName: section.name,
                            sectionSlug,
                            lessonName: lesson.name,
                            lessonSlug,
                            number: ++lessonCounter

                        }
                    })
                }
            }
            )
        }
// console.log('getEnrichedData returning enrichedCourse', enrichedCourse)
        return enrichedCourse;
    })
}

// Returns a single course by slug
export function getEnrichedCourseBySlug(
    dataCourseContent: Course, 
    courseSlug: string
): Course | undefined {
    const enrichedCourses = getEnrichedData(dataCourseContent);
    return enrichedCourses.find(course => course.courseSlug === courseSlug);
}
