export default function getFlatLessons(data) {
    const flatLessons = [];
    data.section.forEach((section) => {
        if (section.lesson && Array.isArray(section.lesson)) {
            section.lesson.forEach((lesson) => {
                flatLessons.push({
                    courseSlug: section.courseSlug,
                    sectionSlug: section.sectionSlug,
                    lessonSlug: lesson.lessonSlug,
                    number: lesson.number,
                    name: lesson.name,
                    type: lesson.type,
                });
            });
        } else {
            flatLessons.push({
                courseSlug: section.courseSlug,
                sectionSlug: section.sectionSlug,
                lessonSlug: section.lessonSlug,
                number: section.number,
                name: section.name,
                type: section.type,
            });
        }
    });
    // return flatLessons
    return flatLessons.sort((a, b) => a.number - b.number);
}
