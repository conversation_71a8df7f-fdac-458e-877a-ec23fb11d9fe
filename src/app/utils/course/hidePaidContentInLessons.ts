import slugify from "@/app/utils/slugify";
import {
    Course,
    Section,
    Lesson
} from "@/types/course";

export default function hidePaidContentInLessons(dataCourseContent: Course): Course | object {
    // Handle null/undefined input
    if (!dataCourseContent) {
        console.error("dataCourseContent is undefined or null");
        return {};
    }

    // Ensure we're working with an object
    const course = Array.isArray(dataCourseContent) ? dataCourseContent[0] : dataCourseContent;

    // Validate array
    // if (!Array.isArray(data)) {
    //     console.error("dataCourseContent is not an array", data);
    //     return [];
    // }

    // return data.map((course) => {
        // Skip processing if course is null/undefined
        if (!course) return course;

        const courseSlug = course.name ? slugify(course.name) : '';
        const courseName = course.name || '';

        const enrichedCourse: Course = {
            ...course,
            courseSlug,
            courseName,
            'section': course.section && Array.isArray(course.section) ? 
                course.section.map((section: Section) => {
                    // Skip processing if section is null/undefined
                    if (!section) return section;
                    const sectionSlug = section.name ? slugify(section.name) : '';
                    
                    return {
                        ...section,
                        sectionSlug,
                        courseSlug,
                        'lesson': section.lesson && Array.isArray(section.lesson) ?
                            section.lesson
                                // .filter((lesson: Lesson) => lesson && lesson.isPaid === false)
                                .map((lesson: Lesson) => {
                                    if (lesson.isPaid === false) return {...lesson}
                                    else return {
                                        ...lesson,
                                        content: null,
                                        video: null,
                                    };
                                }) : []
                    };
                }) : []
        };

        return enrichedCourse;
    // });
}
