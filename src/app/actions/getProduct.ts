'use server';
// import cache from "react";
import productsJSON from "@/data/course";
import { auth } from "@/auth";
import hidePaidContentInLessons from "@/app/utils/course/hidePaidContentInLessons";
import { getDBproductById, getDBproductBySlug  } from "@/controllers/productController";
import { getUserPurchase } from "@/controllers/purchaseController";
import { Course } from "@/types/course";
import { AirtableProduct } from "@/types/airtable";


async function prepareProductForUser(product: AirtableProduct) {
    // Find maching product with the local JSON product
    // Checks whether the user has purchased the product.
    // Modifies the returned content to hide or show paid content based on purchase status.

    const session = await auth();
    let hasPurchased = false;

    const productData = productsJSON.find((p: Course) => p.id === product.id);
    if (!productData) {
        return { error: 'Product data not found' }
    }

    const newProduct = {
        id: product.id,
        ...productData
    }

    // Check if user has purchased the product
    if (session?.user?.email) {
        hasPurchased = (await getUserPurchase(session?.user?.id, product.id)).length > 0;
    }

    if (session?.user?.email && hasPurchased) {
        if (Array.isArray(newProduct)) {
            return newProduct[0];
        } else {
            return newProduct;
        }
    } else {
        if (Array.isArray(newProduct)) {
            return hidePaidContentInLessons(newProduct[0]);
        } else {
            return hidePaidContentInLessons(newProduct);
        }
    }

    return {};
}


export async function getProduct(productId: string) {
    try {
        const productDB = await getDBproductById(productId);
        if (!productDB) return { error: 'Product not found in DB' } 
        
        //  productDB example:
        //  {
        //      "id": "recw2ue2MsPyd66v5",
        //      "name": "Posing guide",
        //      "price": 36,
        //      "priceOld": 51,
        //      "slug": "posing-guide"
        // }

        return prepareProductForUser(productDB);

    } catch (error) {
        console.error('Error fetching product:', error);
        throw error;
    }
}

export async function getProductBySlug(productSlug: string) {
    try {
        const productDB = await getDBproductBySlug(productSlug);
        if (!productDB) return { error: 'Product not found in DB' } 
        return prepareProductForUser(productDB);
    } catch (error) {
        console.error('Error fetching product:', error);
        throw error;
    }
}
