import { NextResponse } from 'next/server';
import Strip<PERSON> from 'stripe';
import { createPurchase } from '@/controllers/purchaseController';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
    apiVersion: '2025-02-24.acacia'
});

export async function POST(req: Request) {
    // console.log('***** Webhook received *****');
    const payload = await req.text();
    const sig = req.headers.get('stripe-signature')!;

    let event: Stripe.Event;

    try {
        event = stripe.webhooks.constructEvent(
            payload,
            sig,
            process.env.STRIPE_WEBHOOK_SECRET!
        );
    } catch (err) {
        console.error('Webhook signature verification failed:', err);
        return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    if (event.type === 'checkout.session.completed') {
        const session = event.data.object as Stripe.Checkout.Session;
        const { customer_email, metadata, client_reference_id } = session;

        if (!customer_email || !metadata?.productName || !metadata?.productId || !client_reference_id) {
            console.error('Missing required fields in session:', session);
            return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
        }

        try {

            // Record the purchase in your database
            // console.log('Recording purchase in Airtable...2');
            await createPurchase(
                client_reference_id, // userId
                customer_email,
                metadata.productName,
                metadata.productId
            );

            // Log successful purchase
            // console.log('Purchase recorded successfully2:', {
            //     userId: client_reference_id,
            //     email: customer_email,
            //     productName: metadata.productName,
            //     productId: metadata.productId
            // });

            return NextResponse.json({ success: true });
        } catch (error) {
            console.error('Error recording purchase:', error);
            return NextResponse.json({ error: 'Failed to record purchase' }, { status: 500 });
        }
    }

    return NextResponse.json({ received: true });
} 
