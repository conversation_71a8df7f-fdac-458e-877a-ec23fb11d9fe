// src/app/api/purchase/route.ts
import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { auth } from "@/auth";
import { getDBproductBySlug } from '@/controllers/productController';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
    apiVersion: '2025-02-24.acacia',
});

export async function POST(req: NextRequest) {
    try {
        const session = await auth();
        if (!session || !session.user?.email) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { email, productType, productId, userId } = await req.json();

        if (!email || !productType || !productId) {
            return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
        }

        // Log the userId for debugging
        // console.log('Purchase API received userId:', userId);

        // Get product from Airtable instead of using mock data
        const product = await getDBproductBySlug(productId);
        if (!product) {
            return NextResponse.json({ error: 'Product not found' }, { status: 404 });
        }

        const baseUrl = req.headers.get('origin') || process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
        const checkoutSession = await stripe.checkout.sessions.create({
            client_reference_id: userId,
            payment_method_types: ['card'],
            line_items: [
                {
                    price_data: {
                        currency: 'usd',
                        product_data: { name: product.name },
                        unit_amount: product.price,
                    },
                    quantity: 1,
                },
            ],
            mode: 'payment',
            customer_email: email,
            success_url: `${baseUrl}/course/purchase-success?session_id={CHECKOUT_SESSION_ID}&product_type=${productType}&product_id=${productId}`,
            cancel_url: `${baseUrl}/course/${productId}?canceled=true`,
            metadata: {
                productName: product.name,
                productType: product.type,
                productId: productId
            }
        });

        return NextResponse.json({ sessionId: checkoutSession.id });
    } catch (error) {
        console.error('Error creating checkout session:', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'Internal server error' },
            { status: 500 }
        );
    }
}
