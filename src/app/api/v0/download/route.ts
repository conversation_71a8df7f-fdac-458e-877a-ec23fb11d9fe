import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { auth } from '@/lib/auth';


export async function GET(req: NextRequest) {
    const session = await auth();

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // 🔐 Optional: Check purchase access from DB here
  const userHasAccess = true; // <-- Replace with real check
  if (!userHasAccess) {
    return NextResponse.json({ error: 'Access denied' }, { status: 403 });
  }

  const fileName = req.nextUrl.searchParams.get('file');
  if (!fileName) {
    return NextResponse.json({ error: 'No file specified' }, { status: 400 });
  }

  const filePath = path.join(process.cwd(), 'private_files', fileName);

  if (!fs.existsSync(filePath)) {
    return NextResponse.json({ error: 'File not found' }, { status: 404 });
  }

  const fileBuffer = fs.readFileSync(filePath);

  return new NextResponse(fileBuffer, {
    status: 200,
    headers: {
      'Content-Type': 'application/pdf', // change based on file type
      'Content-Disposition': `attachment; filename="${fileName}"`,
    },
  });
}
