import { NextRequest, NextResponse } from 'next/server';
import { auth } from "@/auth";
import { base } from "@/lib/airtable";
import { User } from '@/types/user';

// GET /api/users - Get all users
export async function GET() {
    try {
        // Check authentication
        const session = await auth();
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Fetch users from Airtable
        const records = await base('Users').select().firstPage();

        const users: User[] = records.map(record => ({
            id: record.id,
            email: record.fields.email as string,
            name: record.fields.name as string | null,
            emailVerified: record.fields.emailVerified ? new Date(record.fields.emailVerified as string) : null,
        }));

        return NextResponse.json(users);
    } catch (error) {
        console.error('Error fetching users:', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'Internal server error' },
            { status: 500 }
        );
    }
}

// POST /api/users - Add a new user
export async function POST(req: NextRequest) {
    try {
        // Check authentication
        const session = await auth();
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Get user data from request body
        const userData = await req.json();
        const { email, name } = userData;

        if (!email) {
            return NextResponse.json({ error: 'Email is required' }, { status: 400 });
        }

        // Check if user already exists
        const existingRecords = await base('Users').select({
            filterByFormula: `{email} = '${email}'`,
        }).firstPage();

        if (existingRecords.length > 0) {
            return NextResponse.json({ error: 'User with this email already exists' }, { status: 409 });
        }

        // Create new user
        const records = await base('Users').create([
            {
                fields: {
                    email: email,
                    name: name || null,
                    emailVerified: new Date().toISOString(),
                },
            },
        ]);

        const newUser = {
            id: records[0].id,
            email: records[0].fields.email,
            name: records[0].fields.name,
            emailVerified: records[0].fields.emailVerified ? new Date(records[0].fields.emailVerified as string) : null,
        };

        return NextResponse.json(newUser, { status: 201 });
    } catch (error) {
        console.error('Error creating user:', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'Internal server error' },
            { status: 500 }
        );
    }
}
