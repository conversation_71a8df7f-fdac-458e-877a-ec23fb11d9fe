import { NextRequest, NextResponse } from 'next/server';
import { auth } from "@/auth";
import { base } from "@/lib/airtable";
import { User } from '@/types/user';

// GET /api/users/[userId] - Get a user by ID
export async function GET(
    _req: NextRequest,
    { params }: { params: Promise<{  userId: string }> }
) {
    const { userId } = await params;

    try {
        // Check authentication
        const session = await auth();
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }
        // Fetch user from Airtable
        try {
            const record = await base('Users').find(userId);

            const user: User = {
                id: record.id,
                email: record.fields.email as string,
                name: record.fields.name as string | null,
                emailVerified: record.fields.emailVerified ? new Date(record.fields.emailVerified as string) : null,
            };

            return NextResponse.json(user);
        } catch (error) {
            return NextResponse.json({ error: `User not found. ${error}` }, { status: 404 });
        }
    } catch (error) {
        console.error('Error fetching user:', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'Internal server error' },
            { status: 500 }
        );
    }
}

// PUT /api/users/[userId] - Update user info
export async function PUT(
    req: NextRequest,
    { params }: { params: Promise<{  userId: string }> }
) {
    const { userId } = await params;

    try {
        // Check authentication
        const session = await auth();
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const userData = await req.json();
        const { name, email } = userData;

        // Validate input
        if (!name && !email) {
            return NextResponse.json({ error: 'No fields to update' }, { status: 400 });
        }

        // Check if user exists
        try {
            await base('Users').find(userId);
        } catch (error) {
            return NextResponse.json({ error: `User not found. ${error}` }, { status: 404 });
        }

        // Prepare fields to update
        const fields: Record<string, string> = {};
        if (name) {
            fields.name = name;
        }
        if (email) {
            fields.email = email;
        }

        // Update user
        const updatedRecord = await base('Users').update(userId, fields);

        const updatedUser = {
            id: updatedRecord.id,
            email: updatedRecord.fields.email,
            name: updatedRecord.fields.name,
            emailVerified: updatedRecord.fields.emailVerified ? new Date(updatedRecord.fields.emailVerified as string) : null,
        };

        return NextResponse.json(updatedUser);
    } catch (error) {
        console.error('Error updating user:', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'Internal server error' },
            { status: 500 }
        );
    }
}

// DELETE /api/users/[userId] - Delete a user
export async function DELETE(
    _req: NextRequest,
    { params }: { params: Promise<{  userId: string }> }
) {
    const { userId } = await params;

    try {
        // Check authentication
        const session = await auth();
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Check if user exists
        try {
            await base('Users').find(userId);
        } catch (error) {
            return NextResponse.json({ error: `User not found, ${error}` }, { status: 404 });
        }

        // Delete user
        await base('Users').destroy(userId);

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error('Error deleting user:', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'Internal server error' },
            { status: 500 }
        );
    }
}
