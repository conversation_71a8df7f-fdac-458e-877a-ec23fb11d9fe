import { NextRequest, NextResponse } from 'next/server';
import { auth } from "@/auth";
import { getUserPurchases, createPurchase } from '@/controllers/purchaseController';
import { getUserById } from '@/controllers/userController';

export async function POST(
    req: NextRequest,
    { params }: { params: Promise<{  userId: string }> }
) {
    const { userId } = await params;

    try {
        const session = await auth();
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const body = await req.json();
        const { productName } = body;

        if (!productName) {
            return NextResponse.json({ error: 'Product name is required' }, { status: 400 });
        }

        const user = await getUserById(userId);
        if (!user) {
            return NextResponse.json({ error: 'User not found' }, { status: 404 });
        }

        const purchase = await createPurchase(userId, String(user.email), productName );

        return NextResponse.json({
            success: true,
            purchase
        });

    } catch (error) {
        console.error('Error creating purchase:', error);
        return NextResponse.json({
            error: 'Internal server error',
            details: error instanceof Error ? error.message : String(error)
        }, { status: 500 });
    }
}



export async function GET(
    _req: NextRequest,
    { params }: { params: Promise<{  userId: string }> }
) {
    const { userId } = await params;

    try {
        const session = await auth();
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const user = await getUserById(userId);

        if (!user) {
            return NextResponse.json({ error: 'User not found' }, { status: 404 });
        }

        const purchases = await getUserPurchases(userId, String(user.email));
        return NextResponse.json(purchases);
    } catch (error) {
        console.error('Error in GET /api/v0/users/[userId]/products:', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'Internal server error' },
            { status: 500 }
        );
    }
}

