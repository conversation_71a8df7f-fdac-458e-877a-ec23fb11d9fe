import { NextRequest, NextResponse } from 'next/server';
import { auth } from "@/auth";
import { base } from "@/lib/airtable";
import { createPurchase, getUserPurchase } from '@/controllers/purchaseController';
import { getDBproductById } from '@/controllers/productController';
import { getUserById } from '@/controllers/userController';

export async function DELETE(
    _req: NextRequest,
    { params }: { params: Promise<{ userId: string; productId: string }> }

) {
    const { userId, productId } = await params;
    
    try {
        const session = await auth();
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }


        // Get user email from userId
        let userEmail;
        try {
            const userRecord = await base('Users').find(userId);
            userEmail = userRecord.fields.email;
        } catch (error) {
            return NextResponse.json({ error: `User not found. ${error}` }, { status: 404 });
        }

        // Verify purchase exists and belongs to the user
        try {
            const purchaseRecord = await base('Purchases').find(productId);
            const purchaseEmail = purchaseRecord.fields.email;

            if (purchaseEmail !== userEmail) {
                return NextResponse.json({ error: 'Purchase does not belong to this user' }, { status: 403 });
            }
        } catch {
            return NextResponse.json({ error: 'Purchase not found' }, { status: 404 });
        }

        // Delete purchase
        await base('Purchases').destroy(productId);

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error('Error deleting purchase:', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'Internal server error' },
            { status: 500 }
        );
    }
}

export async function GET(
    _req: NextRequest,
    { params }: { params: Promise<{ userId: string; productId: string }> }
) {
    const { userId, productId } = await params;

    try {
        let purchases;
        try {
            const purchases = await getUserPurchase(userId, productId);
            return NextResponse.json({ ... purchases });
        } catch {
            return NextResponse.json({ error: 'Failed to fetch purchases' }, { status: 500 });
        }

        return NextResponse.json(purchases);
    } catch (error) {
        console.error('Error fetching purchase  :', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'Internal server error' },
            { status: 500 }
        );
    }
}

export async function POST(
    _req: NextRequest,
    { params }: { params: Promise<{ userId: string; productId: string }> }
) {
    // Access userId and productId directly from params
    const { userId, productId } = await params;

    try {
        const session = await auth();
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }



        let product;
        try {
            product = await getDBproductById(productId);
        } catch {
            return NextResponse.json({ error: 'Product not found' }, { status: 404 });
        }

        let user;
        try {
            user = await getUserById(userId);
        } catch {
            return NextResponse.json({ error: 'User not found' }, { status: 404 });
        }

        try {
            createPurchase(user.id, user.email, product.name, product.id);
        } catch (error) {
            return NextResponse.json({ error: `Purchase not made. ${error}` }, { status: 404 });
        }

        return NextResponse.json({
            "productName": product.name,
            "productId": product.id,
            "clientEmail": user.email,
            "clientId": user.id
        });

    } catch (error) {
        console.error('Error fetching purchase  :', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'Internal server error' },
            { status: 500 }
        );
    }
}

