import productsJSON from "@/data/course";
import { NextRequest, NextResponse } from "next/server";
import { getDBproductById } from "@/controllers/productController";
import { Course } from "@/types/course";

export async function GET(
    _req: NextRequest,
    { params }: { params: Promise<{ productId: string }> }
) {
    const { productId } = await params;

    try {

        if (!productId) {
            return NextResponse.json(
                { error: 'Product ID not provided' },
                { status: 400 }
            );
        }

        // console.log('Looking up product with ID:', productId); // Debug log

        const product = await getDBproductById(productId);

        if (!product) {
            return NextResponse.json(
                { error: 'Product not found' },
                { status: 404 }
            );
        }

        const productData = productsJSON.find((p: Course) => p.id === productId);
        if (!productData) {
            return NextResponse.json(
                { error: 'Product data not found' },
                { status: 404 }
            );
        }

        return NextResponse.json({
            id: product.id,
            ...productData
        });
    } catch (error) {
        console.error('Error fetching product:', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'Internal server error' },
            { status: 500 }
        );
    }
}
