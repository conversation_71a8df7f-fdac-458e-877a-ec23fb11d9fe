import { NextRequest, NextResponse } from 'next/server';
import { getDBproductById, getDBproductBySlug, getDBproducts } from "@/controllers/productController";

export async function POST(req: NextRequest) {
    try {
        const body = await req.json();
        // console.log('Received request body:', body); // Debug log

        const { productSlug, productId } = body;
        // console.log('Extracted values:', { productSlug, productId }); // Debug log

        if (!productSlug && !productId) {
            return NextResponse.json(
                { error: 'Either productSlug or productId is required' },
                { status: 400 }
            );
        }

        let product;
        if (productId) {
            product = await getDBproductById(productId);
        } else if (productSlug) {
            product = await getDBproductBySlug(productSlug);
        }

        if (!product) {
            return NextResponse.json(
                { error: 'Product not found' },
                { status: 404 }
            );
        }

        return NextResponse.json(product);
    } catch (error) {
        console.error('Error fetching product:', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'Internal server error' },
            { status: 500 }
        );
    }
}

export async function GET() {
    try {
        const products = await getDBproducts();
        return NextResponse.json(products);
    }
    catch (error) { // error is caught but not used in the function body
        console.error('Error fetching product:', error); // error is only used in console.log
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'Internal server error' },
            { status: 500 }
        );
    }
}
