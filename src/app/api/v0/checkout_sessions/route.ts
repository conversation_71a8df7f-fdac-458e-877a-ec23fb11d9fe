// src/app/api/v0/checkout_sessions/route.ts
import { NextRequest, NextResponse } from 'next/server';
import Stripe from 'stripe';
import { auth } from "@/auth";
// import { getDBproductById } from "@/controllers/productController"
import { getProduct } from "@/app/actions/getProduct"; 
import { getDBproductById } from "@/controllers/productController";
import slugify from "@/app/utils/slugify";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
    apiVersion: '2025-02-24.acacia',
});

export async function POST(req: NextRequest) {

    console.log('***** Creating checkout session *****');

    try {
        const session = await auth();
        if (!session || !session.user?.email) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { email, productType, productId, userId } = await req.json();
        if (!email || !productType || !productId) {
            return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
        }

        // Get product from Airtable
        // const product = await getProduct(productId);
        // // const product = await getDBproductBySlug(productSlug);
        // if (!product) {
        //     return NextResponse.json({ error: 'Product not found' }, { status: 404 });
        // }
        const product = await getDBproductById(productId);
        if (!product) {
            return NextResponse.json({ error: 'Product not found' }, { status: 404 });
        }

        console.log('Creating checkout session for product:', product);
        // console.log('*** product.courseSlug:', product.courseSlug);

        const baseUrl = req.headers.get('origin') || process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
     
        const checkoutSession = await stripe.checkout.sessions.create({
            client_reference_id: userId,
            payment_method_types: ['card'],
            line_items: [
                {
                    price_data: {
                        currency: process.env.NEXT_PUBLIC_CURRENCY || 'eur',
                        product_data: { name: product.name },
                        unit_amount: product.price * 100,
                    },
                    quantity: 1,
                },
            ],
            mode: 'payment',
            customer_email: email,
            success_url: `${baseUrl}/course/purchase-success?session_id={CHECKOUT_SESSION_ID}&product_type=${productType}&product_id=${productId}`,
            cancel_url: `${baseUrl}/course/${slugify(product.name)}?canceled=true`,
            metadata: {
                productName: product.name,
                productType: product.type,
                productSlug: slugify(product.name),
                productId: product.id,
            } 
        });

        // console.log('Checkout session created:', checkoutSession);
        return NextResponse.json({ sessionId: checkoutSession.id });
    } catch (error) {
        console.error('Error creating checkout session:', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'Internal server error' },
            { status: 500 }
        );
    }
}