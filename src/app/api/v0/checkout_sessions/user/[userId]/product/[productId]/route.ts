import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import slugify from "@/app/utils/slugify";
import { auth } from "@/auth";
import { getDBproductById } from "@/controllers/productController";
import { getUserById } from "@/controllers/userController";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
    apiVersion: '2025-02-24.acacia',
});

// export async function POST(
//     req: NextRequest,
//     { params }: { params: { userId: string; productId: string } }
// ) {
export async function POST(req: NextRequest, 
    { params }: { params: Promise<{ userId: string; productId: string }> }) {

    const { userId, productId } = await params;
    try {
        const session = await auth();
        if (!session || !session.user?.email) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // const { userId, productId } = params;
        if (!productId || !userId) {
            return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
        }

        // Get user from Airtable
        const user = await getUserById(userId);
        if (!user) {
            return NextResponse.json({ error: 'User not found' }, { status: 404 });
        }

        // Get product from Airtable
        const product = await getDBproductById(productId);
        if (!product) {
            return NextResponse.json({ error: 'Product not found' }, { status: 404 });
        }

        const baseUrl = req.headers.get('origin') || process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
        const checkoutSession = await stripe.checkout.sessions.create({
            // client_reference_id: userId,
            client_reference_id: String(userId),
            payment_method_types: ['card'],
            line_items: [
                {
                    price_data: {
                        currency: process.env.NEXT_PUBLIC_CURRENCY || 'eur',
                        // product_data: { name: product.name },
                        // unit_amount: product.price * 100,
                        product_data: { name: String(product.name) },
                        unit_amount: Number(product.price) * 100,
                    },
                    quantity: 1,
                },
            ],
            mode: 'payment',
            // customer_email: user.email,
            customer_email: String(user.email),
            success_url: `${baseUrl}/course/purchase-success?session_id={CHECKOUT_SESSION_ID}&product_type=${product.type}&product_slug=${slugify(product.name)}`,
            cancel_url: `${baseUrl}/course/${slugify(String(product.name))}?canceled=true`,
            metadata: {
                // productName: product.name,
                // productType: product.type,
                // productSlug: slugify(product.name),
                // productId: product.id,
                productName: String(product.name),
                productType: String(product.type),
                productSlug: slugify(String(product.name)),
                productId: String(product.id),
            }
        });

        return NextResponse.json({ sessionId: checkoutSession.id });
    } catch (error) {
        console.error('Error creating checkout session:', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'Internal server error' },
            { status: 500 }
        );
    }
}
