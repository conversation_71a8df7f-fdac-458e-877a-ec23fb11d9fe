import { NextResponse } from 'next/server';
import { auth } from "@/auth";
import { stripe } from '@/lib/stripe';

export async function POST(req: Request) {
    try {
        const session = await auth();
        if (!session?.user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const body = await req.json();
        const { sessionId } = body;

        // Just verify the payment status
        const stripeSession = await stripe.checkout.sessions.retrieve(sessionId);

        if (stripeSession.payment_status !== 'paid') {
            return NextResponse.json({ error: 'Payment not completed' }, { status: 400 });
        }

        // Don't create purchase record - let webhook handle it
        return NextResponse.json({ success: true }, { status: 200 });
    } catch (error) {
        console.error('Error completing purchase:', error);
        return NextResponse.json({ error: 'Failed to complete purchase' }, { status: 500 });
    }
}
