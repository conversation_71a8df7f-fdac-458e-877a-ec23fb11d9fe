import { SendGridMailer } from '@/lib/mailer'
import { NextResponse } from 'next/server'

export async function POST(req: Request) {
    try {
        const { email, url } = await req.json()

        await SendGridMailer.sendVerificationRequest({
            identifier: email,
            url: url,
            provider: {
                server: {
                    host: "smtp.sendgrid.net",
                    port: 587,
                    auth: {
                        user: "apikey",
                        pass: process.env.AUTH_SENDGRID_KEY,
                    }
                },
                from: process.env.AUTH_EMAIL_FROM,
            },
        })

        return NextResponse.json({ success: true })
    } catch (error) {
        console.error('Failed to send verification email:', error)
        return NextResponse.json(
            { error: 'Failed to send verification email' },
            { status: 500 }
        )
    }
}