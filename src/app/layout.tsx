import type { Metada<PERSON> } from "next";
import "@/app/globals.css";
import NextAuthSessionProvider from "@/app/providers/SessionProvider";

export const metadata: Metadata = {
  title: "Tati Frank Education - Photography Education",
  description: "Education for photographers to capture their own stories"
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  return (
    <NextAuthSessionProvider>

      <html lang="en">
        <body
        // className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        >
          {children}
        </body>
      </html>

    </NextAuthSessionProvider>


  );
}
