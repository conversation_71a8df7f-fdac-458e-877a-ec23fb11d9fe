@import url("https://use.typekit.net/moc7fba.css");

@import "tailwindcss";

/* @tailwind base;
@tailwind components;
@tailwind utilities; */


@theme {
  --font-serif: "ff-more-web-pro", "serif";
  --font-hand: "cinque-donne-pro", "serif";
  --font-hand2: "adobe-handwriting-ernie", "sans-serif";
  --font-heading: "ivymode", sans-serif;
  /* --font-body: "ff-more-web-pro", "serif"; */
  /* --font-body: "ivymode", sans-serif; */
  --font-body: "le-monde-livre-std", serif;
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-transparent: transparent;
  --color-white: #fff;
  --color-black: #020D07;
  --color-primary: #B5876E;
  --color-primary-50: #FFFFFF;
  --color-primary-100: #FAF7F5;
  --color-primary-150: #f7f0eb;
  --color-primary-200: #E9DBD3;
  --color-primary-300: #D7BFB2;
  --color-primary-400: #C6A390;
  --color-primary-500: #B5876E;
  --color-primary-600: #9F6C51;
  --color-primary-700: #7D5540;
  --color-primary-800: #5B3E2F;
  --color-primary-900: #3A271D;

  --z-header: 10;

  /* --spacing: 1px; */
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html {
  scroll-behavior: smooth;
}

body {
  /* color: var(--foreground);
  background: var(--background); */
  font-family: var(--font-body);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-heading);
}

.handwriten {
  font-family: var(--font-hand2) !important;
}

.handwriten2 {
  font-family: var(--font-hand2);
}

.z-header {
  z-index: var(--z-header);
}

/* font-family: "ivymode", sans-serif;
font-weight: 400;
font-style: normal; */
/* IvyMode Italic
font-family: "ivymode", sans-serif;
font-weight: 400;
font-style: italic;
IvyMode Bold
font-family: "ivymode", sans-serif;
font-weight: 700;
font-style: normal;
IvyMode Bold Italic
font-family: "ivymode", sans-serif;
font-weight: 700;
font-style: italic; */

@media (width >=48rem) {
  .md\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
}

.article {
  p {
    font-size: 1.125rem; 
    margin-bottom: 1rem;
  }
}

.article.blur {
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none; /* Standard syntax */
}