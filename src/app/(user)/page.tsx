'use client';
import coursesData from '@/data/course/index.js';
import PageTitle from "@/components/features/page-title";
import { Container, Row } from "@/ui/index";
import ProductCardList from "@/components/features/product/ProductCardList";
import Image from "next/image";
import Hero from "@/components/features/hero";
import VideoPlayer from "@/components/features/video/VideoPlayer";

export default function Home() {

  return (

    <>
      {/* <Hero
        backgroundImage="/temp/kt.jpg"
      /> */}
      <Hero
        backgroundImage="/images/hero.jpg"
      />

      <Container size="sm" className="relative">
        <div className="my-8">
          <VideoPlayer
            thumbnailVideo="/temp/AdobeStock_569093933_Video_HD_Preview.mp4"
            video="/temp/AdobeStock_569093933_Video_HD_Preview.mp4"
            poster="/temp/kt.jpg"
            className="-mt-16"
            mode="modal"
            showThumbnail
          />
        </div>

        <div className="flex flex-col justify-center items-center min-h-[calc(100vh-150px)]">
          <PageTitle
            subtitle="Photography education"
          // backgroundImage="/temp/kt.jpg"
          >Tati <PERSON></PageTitle>

          <p className="text-xl text-center mb-5">
            Become immersed in Tati Frank’s knowledge, creativity, and hers incredibly fascinating Photographic World – Designed
            to inspire, teach, and reignite your creativity.
          </p>

          {/* <p className="text-lg text-center">
            The focus is to teach you how Tati works technically, and how she works
            to achieves such a high standard of emotive, breathtaking and captivating photography; and how you can do this yourself!
          </p> */}

          {/* 
        <Row className="bg-primary-150 p-5 flex flex-colborder-l-8 border-l-8 border-primary-200">
          <h1>Home page</h1>
        </Row> */}
        </div>
      </ Container>

      <Container className="flex gap-3">
        <Image src="/portfolio/11.jpg" alt="Tati Frank in a bright, airy room with curtains and plants" width={320} height={920} className="w-full h-auto" />
        <Image src="/portfolio/12.jpg" alt="Tati Frank in a bright, airy room with curtains and plants" width={320} height={920} className="w-full h-auto" />
        <Image src="/portfolio/13.jpg" alt="Tati Frank in a bright, airy room with curtains and plants" width={320} height={920} className="w-full h-auto" />

      </Container>

          <ProductCardList products={coursesData} />
    </>
  )
}

