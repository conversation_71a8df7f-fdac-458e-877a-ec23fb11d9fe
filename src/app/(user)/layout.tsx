import type { Metada<PERSON> } from "next";
import "@/app/globals.css";
import Menu from "@/components/features/menu";
import Footer from "@/components/features/footer";
const menu = [
  {
    name: "Home",
    href: "/",
  },
  {
    name: "Photography Course",
    highlight: false,
    href: "/course/complete-photography-course",
  },
  {
    name: "Posing Guide eBook",
    // highlight: true,
    href: "/course/posing-guide",
  }
];

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="flex flex-col min-h-screen">
      <Menu data={menu} />
      {children}
      <Footer />
    </div>

  );
}
