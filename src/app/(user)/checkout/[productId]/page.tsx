'use client';

import React, { useEffect, useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { useParams, useRouter } from 'next/navigation';
import { useSession, signIn } from 'next-auth/react';
import ErrorMessage from '@/components/features/error-message';

import { getUserPurchaseStatus } from '@/controllers/purchaseController';
import { getProduct } from '@/app/actions/getProduct';

export default function CheckoutPage() {
  const params = useParams();
  const router = useRouter();
  const productId = params.productId as string;
  const { data: session, status } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const [purchasedStatus, setPurchasedStatus] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initiateCheckout = async () => {

      setIsLoading(true);

      try {

        const userId = session?.user?.id;
        const email = session?.user?.email;

        if (status === 'unauthenticated') return signIn();
        if (status !== 'authenticated' || !session?.user) {
          return; // Still loading session
        }

        if (session?.user?.id && productId) {
          const { hasPurchased } = await getUserPurchaseStatus(session.user.id, productId);

          if (hasPurchased) {
            setPurchasedStatus(true);
            await redirectToFirstLesson(productId, router)
          } else {
            await handleStripeCheckout({ userId, email, productId })
          }
        }

      } catch (err) {
        console.error('Checkout error:', err);
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    initiateCheckout();
  }, [status, session, productId, router]);

  if (error) {

    return (
      <ErrorMessage
        backlink={[
          // { label: 'Try again', href: '/' },
          { label: 'Go back', onClick: () => router.back() },
        ]}
      >
        {error}
      </ErrorMessage>
    );
  }
  if (purchasedStatus) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center p-8">
          <h1 className="text-2xl mb-4">
            You already bought the course
          </h1>
          <div className="animate-spin h-8 w-8 border-4 border-primary-500 rounded-full border-t-transparent mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center min-h-screen">
      <div className="text-center p-8">
        <h1 className="text-2xl mb-4">
          {isLoading ? 'Preparing your checkout...' : 'Redirecting to checkout...'}
        </h1>
        <div className="animate-spin h-8 w-8 border-4 border-primary-500 rounded-full border-t-transparent mx-auto"></div>
      </div>
    </div>
  );
}

async function redirectToFirstLesson(productId: string, router: ReturnType<typeof useRouter>) {

  const product = await getProduct(productId);

  const firstSection = product.section[0];
  const firstLesson = firstSection.lesson[0];

  router.push(`/course/${firstSection.courseSlug}/${firstSection.sectionSlug}/${firstLesson.lessonSlug}`);

}
async function handleStripeCheckout({ userId, email, productId }: { userId: string, email: string, productId: string }) {
  // 1. Fetch product details
  const productResponse = await fetch('/api/v0/products', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ productId }),
  });

  if (!productResponse.ok) {
    throw new Error('Failed to fetch product details');
  }

  // 2. Create Stripe checkout session
  const checkoutResponse = await fetch('/api/v0/checkout_sessions', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      email: email,
      productType: 'course',
      productId: productId,
      userId: userId
    }),
  });

  if (!checkoutResponse.ok) {
    const errorData = await checkoutResponse.json();
    throw new Error(errorData.error || 'Failed to create checkout session');
  }

  const { sessionId } = await checkoutResponse.json();

  // 3. Redirect to Stripe checkout
  const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);
  if (!stripe) {
    throw new Error('Failed to load payment system');
  }

  const { error: stripeError } = await stripe.redirectToCheckout({ sessionId });
  if (stripeError) {
    throw stripeError;
  }

}
