// 'use client';

// import { useEffect, useState } from 'react';
// import { useSearchParams } from 'next/navigation';
// import { getProduct } from '@/app/actions/getProduct';
// import ErrorMessage from '@/components/features/error-message';
// import { Course } from '@/types/course';

// export default function PurchaseCancel() {
//     const searchParams = useSearchParams();
//     const courseId = searchParams.get('course_id');
//     const [currentProduct, setCurrentProduct] = useState<Course | null>(null);

//     // http://localhost:3000/course/purchase-cancel?course_Id=recw2ue2MsPyd66v5
//     useEffect(() => {
//         const course = async () => {
//             try {
//                 const product = await getProduct(courseId);
//                 setCurrentProduct(product);
//                 // console.log('product', product)
//             }
//             catch (error) {
//                 console.error('Purchase completion error:', error);
//             }
//         };

//         if (courseId) {
//             course();
//         }

//         // console.log('course', course())

//     }, [courseId]);

//     return (
//         <>
//             {currentProduct ? <ErrorMessage
//                 backlink={[
//                     { label: 'Try again', href: '/' },
//                     { label: 'Back to course', href: `/course/${currentProduct?.courseSlug}` },
//                 ]}
//             >
//                 Something went wronge.
//                 The purchase was cancelled
//             </ErrorMessage> : <ErrorMessage
//                 backlink={[
//                     { label: 'Back home', href: `/` },
//                 ]}
//             >
//                 Course not found. Please contact us
//             </ErrorMessage>}
//         </>

//     );
// }

'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { getProduct } from '@/app/actions/getProduct';
import ErrorMessage from '@/components/features/error-message';
import { Course } from '@/types/course';

function PurchaseCancelContent() {
    const searchParams = useSearchParams();
    const courseId = searchParams.get('course_id');
    const [currentProduct, setCurrentProduct] = useState<Course | null>(null);

    // http://localhost:3000/course/purchase-cancel?course_Id=recw2ue2MsPyd66v5
    useEffect(() => {
        const course = async () => {
            try {
                const product = await getProduct(courseId);
                setCurrentProduct(product);
                // console.log('product', product)
            }
            catch (error) {
                console.error('Purchase completion error:', error);
            }
        };

        if (courseId) {
            course();
        }

        // console.log('course', course())

    }, [courseId]);

    return (
        <>
            {currentProduct ? <ErrorMessage
                backlink={[
                    { label: 'Try again', href: `/checkout/${courseId}` },
                    { label: 'Back to course', href: `/course/${currentProduct?.courseSlug}` },
                ]}
            >
                Something went wronge.
                The purchase was cancelled
            </ErrorMessage> : <ErrorMessage
                backlink={[
                    { label: 'Back home', href: `/` },
                ]}
            >
                Course not found. Please contact us
            </ErrorMessage>}
        </>
    );
}

export default function PurchaseCancel() {
    return (
        <Suspense fallback={
            <ErrorMessage
                backlink={[
                    { label: 'Back home', href: '/' },
                ]}
            >
                Loading...
            </ErrorMessage>
        }>
            <PurchaseCancelContent />
        </Suspense>
    );
}