import { Container, Row } from "@/ui/index";
import coursesData from '@/data/course/index.js';
import ProductPromoBanner from "@/components/features/product/productPromoBanner";
import PageTitle from "@/components/features/page-title";

export default function Home() {
  return (
    <Container className="mt-10">
      <Row>
        <PageTitle>Courses</PageTitle>
      </Row>
      <Row>
        {coursesData.map(course => (

          <ProductPromoBanner
            product={course}
            key={course.name}
          />

        ))}
      </Row>
      <Row className="mt-10 flex justify-center text-neutral-600">
        More courses coming soon.
      </Row>
    </Container>
  );
}
