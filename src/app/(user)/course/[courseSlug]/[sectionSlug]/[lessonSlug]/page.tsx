// src/app/(user)/course/[courseSlug]/[sectionSlug]/[lessonSlug]/page.tsx
import { auth } from "@/auth";

// DATA
import rawDataCourseContent from '@/data/course/index.js';


// UTILITIES
import getCourseData from '@/lib/getCourseData';

// LAYOUT
import { Container } from '@/ui/index';
import PageTitle from '@/components/features/page-title';
import { Row, Column, Button } from '@/ui/index';
import Lesson from '@/components/features/course/lesson';


// COMPONENTS
import LessonTableOfContents from "@/components/features/course/tableOfContents";
import ErrorMessage from '@/components/features/error-message';

export default async function LessonPage({ params }: { params: Promise<{ courseSlug: string; sectionSlug: string; lessonSlug: string }> }) {

    const { courseSlug, sectionSlug, lessonSlug } = await params;
    const session = await auth();
    // console.log('session', session)

    const {
        currentCourse,
        currentLesson,
        nextLesson,
        nextFree<PERSON>esson,
        hasPurchased,
    } = await getCourseData({
        rawData: rawDataCourseContent,
        courseSlug: courseSlug,
        sectionSlug: sectionSlug,
        lessonSlug: lessonSlug,
        session: session ?? undefined
    });

    if (!currentLesson) {
        return <Container>
            <ErrorMessage
                backlink={[
                    { label: 'Back to course', href: `/course/${courseSlug}` },
                ]}
            >
                Lesson not found
            </ErrorMessage>
        </Container>;
    }

    return (
        <>
            {currentCourse && currentCourse.attachment && hasPurchased && (
                <div className="w-full p-10 bg-primary-150 mb-5 flex justify-center items-center">
                    <div className="flex justify-between items-center gap-5">

                        <div>
                            <h2 className="text-2xl font-bold">Download
                                &quot;{currentCourse.attachment.name}&quot;
                            </h2>
                            <p className="text-sm italic">Version {currentCourse.attachment.version}</p>
                        </div>
                        <Button
                            href={currentCourse.attachment.url}
                            className="bg-blue-500 transition delay-150 duration-300 ease-in-out hover:-translate-y-1 hover:scale-110 hover:bg-indigo-500 ...">
                            Download
                        </Button>
                    </div>
                </div>
            )}

            <Container>
                <PageTitle
                    subtitle={`${currentLesson.type.charAt(0).toUpperCase() + currentLesson.type.slice(1)} ${currentLesson.number?.toString().padStart(2, "0") || "00"}: ${currentLesson.courseName}`}
                >
                    {currentLesson.name || "Lesson Not Found"}
                </PageTitle>


                <Row className='relative'>
                    <Column className=''>


                        <Lesson
                            currentCourse={currentCourse}
                            currentLesson={currentLesson}
                            nextLesson={nextLesson}
                            nextFreeLesson={nextFreeLesson}
                            hasPurchasedCourse={hasPurchased}
                        />
                    </Column>
                    <Column>
                        <LessonTableOfContents
                            course={currentCourse}
                            className="col-span-2"
                            linkscroll={false}
                            showPreviewLabel={!hasPurchased}
                        />
                    </Column>
                </Row>
            </Container >
        </>
    );
}

