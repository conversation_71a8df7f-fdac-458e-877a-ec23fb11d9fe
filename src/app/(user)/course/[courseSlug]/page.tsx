// DATA
// import rawDataCourseContent from '@/data/course/photographyCourseData.json';
import rawDataCourseContent from '@/data/course/index.js';
import { auth } from "@/auth";

// UTILITIES
import { getDBproductBySlug } from '@/controllers/productController';
import { redirect } from 'next/navigation';
import { getUserPurchaseStatus } from '@/controllers/purchaseController';
import getCourseData from '@/lib/getCourseData';

// COMPONENTS
import { Container, Button } from '@/ui/index';
import HeroVideo from '@/features/hero/HeroVideo';
import CourseFeatures from '@/features/course/courseFeatures';
import CoursePaymentSum from '@/features/course/coursePaymentSum';
import LessonTableOfContents from '@/components/features/course/tableOfContents';
import ErrorMessage from '@/components/features/error-message';

// interface PageTitleMeta {
//   duration: string;
//   lessons: string;
//   instructor: string;
// }

interface CoursePage {
  params: {
    courseSlug: string
  },
  searchParams?: {
    success?: string,
    canceled?: string
  }

}

// interface CoursePageProps {
//   params: Promise<{ courseSlug: string }>;
//   searchParams?: Promise<{ success?: string }>;
// }

export default async function CoursePage({ params, searchParams }: CoursePage) {
  const paymentStatus = await searchParams
  const { courseSlug } = await params;
  const session = await auth();

  console.log('paymentStatus', paymentStatus)

  const {
    currentCourse
  } = await getCourseData({
    rawData: rawDataCourseContent,
    courseSlug: courseSlug,
    session: session
  });

  if (!currentCourse) {
    return <Container>
      <ErrorMessage
        backlink={[
          { label: 'Back to courses', href: `/course/` },
        ]}
      >
        Course not found
      </ErrorMessage>
    </Container>;
  }

  // Get product price & ID from Airtable
  const product = await getDBproductBySlug(courseSlug);
  const courseId = product?.id;
  const coursePrice = product?.price
  const coursePriceOld = product?.priceOld


  // Check purchase status with caching
  let hasPurchasedCourse = false;
  if (session?.user?.id && courseId) {
    const { hasPurchased } = await getUserPurchaseStatus(session.user.id, courseId);
    hasPurchasedCourse = hasPurchased;

    // If user has purchased the course, redirect to the first lesson
    if (hasPurchasedCourse) {
      const firstSection = currentCourse.section[0];
      const firstLesson = firstSection.lesson[0];
      redirect(`/course/${courseSlug}/${firstSection.sectionSlug}/${firstLesson.lessonSlug}`);
    }
  }



  return (
    <>
      {paymentStatus.canceled && <div className='bg-primary-150 p-10 justify-center items-center gap-3 flex'>
        <div className="bg-primary-200 p-10 justify-center items-center gap-3 flex">
          <span>
            <div className='text-xl'>The payment didn’t complete</div>
            Want to try again?
          </span>

          <Button
            href={`/checkout/${courseId}`}
            style="dark"
            className="px-4 py-2 rounded"
            aria-label={`Purchase course for ${(coursePrice).toFixed(2)} eur`}
          >
            {`Buy ${(coursePrice).toFixed(2)} ${process.env.NEXT_PUBLIC_CURRENCY_SYMBOL}`}
          </Button>
        </div>
      </div>}
      <HeroVideo
        videoSrc={currentCourse.video || ''}
        thumbnailSrc={currentCourse.thumbnail || ''}
        title={currentCourse.name}
        subtitle={currentCourse.blurb}
        description={currentCourse.description}
        ctaText="Begin Your Journey"
        ctaLink="#Buy"
        duration={currentCourse.duration}
        lessonCount={currentCourse.lessonCount}
      />

      <CourseFeatures course={currentCourse} />

      <Container size='md' className='mt-14'>
        <p className='text-2xl mb-5 leading-9 font-light mt-5'>{currentCourse.blurb}</p>
        <p className='text-lg leading-7 font-light mt-5 mb-10'>{currentCourse.description}</p>
        <h2 className="text-2xl font-semibold mb-4">Lessons</h2>

        <LessonTableOfContents
          course={currentCourse}
          className="mt-4"
          linkscroll={false}
          showPreviewLabel={true}
          showPaidContent={false}
        />
      </Container>

      {!hasPurchasedCourse && (
        <CoursePaymentSum
          course={currentCourse}
          price={coursePrice}
          priceOld={coursePriceOld}
          courseId={courseId}
          userId={session?.user?.id}
        />
      )}
    </>
  );
}
