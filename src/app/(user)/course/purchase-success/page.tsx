// 'use client';

// import { useEffect, useState } from 'react';
// import { useRouter, useSearchParams } from 'next/navigation';
// import { getProduct } from "@/app/actions/getProduct";

// export default function PurchaseSuccessPage() {
//     const router = useRouter();
//     const searchParams = useSearchParams();
//     const [isLoading, setIsLoading] = useState(true);
//     const [error, setError] = useState<string | null>(null);

//     const sessionId = searchParams.get('session_id');
//     const productType = searchParams.get('product_type');
//     const productId = searchParams.get('product_id');

//     useEffect(() => {
//         const completePurchase = async () => {
//             try {
//                 if (!sessionId || !productType || !productId) {
//                     throw new Error('Missing required parameters');
//                 }
//                 // Verify the payment status. Purchase record is done through stripe webhook
//                 const response = await fetch('/api/v0/complete-purchase', {
//                     method: 'POST',
//                     headers: { 'Content-Type': 'application/json' },
//                     // body: JSON.stringify({ sessionId, productType, productSlug }),
//                     body: JSON.stringify({ sessionId }),
//                 });

//                 if (!response.ok) {
//                     const errorData = await response.json();
//                     throw new Error(errorData.error || 'Failed to complete purchase');
//                 }

//                 const product = await getProduct(productId);

//                 // Handle product data whether it's an array or object
//                 const productData = Array.isArray(product) ? product[0] : product;

//                 // Then handle user redirection based on product type
//                 const firstLesson = productData.section[0].lesson[0];
//                 router.push(`/course/${firstLesson.courseSlug}/${firstLesson.sectionSlug}/${firstLesson.lessonSlug}?success=true`);

//             } catch (error) {
//                 console.error('Purchase completion error:', error);
//                 setError(error instanceof Error ? error.message : 'An error occurred');
//             } finally {
//                 setIsLoading(false);
//             }
//         };

//         completePurchase();
//     }, [sessionId, productType, productId, router]);

//     if (isLoading) {
//         return (
//             <div className="p-4">
//                 <h1 className="text-2xl font-bold">Processing your purchase...</h1>
//                 <p>Please wait while we complete your order.</p>
//             </div>
//         );
//     }

//     if (error) {
//         return (
//             <div className="p-4">
//                 <h1 className="text-2xl font-bold text-red-600">Error</h1>
//                 <p>{error}</p>
//                 <button
//                     onClick={() => router.push('/')}
//                     className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
//                 >
//                     Return to Homepage
//                 </button>
//             </div>
//         );
//     }

//     return (
//         <div className="p-4">
//             <h1 className="text-2xl font-bold">Payment Successful!</h1>
//             {productType === 'course' && <p>Redirecting you to your course...</p>}
//             {/* {productType === 'ebook' && downloadUrl && (
//                 <>
//                     <p>Your purchase was successful!</p>
//                     <a href={downloadUrl} download className="mt-4 inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
//                         Download Your Ebook
//                     </a>
//                 </>
//             )} */}
//         </div>
//     );
// }

'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { getProduct } from "@/app/actions/getProduct";

function PurchaseSuccessContent() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const sessionId = searchParams.get('session_id');
    const productType = searchParams.get('product_type');
    const productId = searchParams.get('product_id');

    useEffect(() => {
        const completePurchase = async () => {
            try {
                if (!sessionId || !productType || !productId) {
                    throw new Error('Missing required parameters');
                }
                // Verify the payment status. Purchase record is done through stripe webhook
                const response = await fetch('/api/v0/complete-purchase', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    // body: JSON.stringify({ sessionId, productType, productSlug }),
                    body: JSON.stringify({ sessionId }),
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || 'Failed to complete purchase');
                }

                const product = await getProduct(productId);

                // Handle product data whether it's an array or object
                const productData = Array.isArray(product) ? product[0] : product;

                // Then handle user redirection based on product type
                const firstLesson = productData.section[0].lesson[0];
                router.push(`/course/${firstLesson.courseSlug}/${firstLesson.sectionSlug}/${firstLesson.lessonSlug}?success=true`);

            } catch (error) {
                console.error('Purchase completion error:', error);
                setError(error instanceof Error ? error.message : 'An error occurred');
            } finally {
                setIsLoading(false);
            }
        };

        completePurchase();
    }, [sessionId, productType, productId, router]);

    if (isLoading) {
        return (
            <div className="p-4">
                <h1 className="text-2xl font-bold">Processing your purchase...</h1>
                <p>Please wait while we complete your order.</p>
            </div>
        );
    }

    if (error) {
        return (
            <div className="p-4">
                <h1 className="text-2xl font-bold text-red-600">Error</h1>
                <p>{error}</p>
                <button
                    onClick={() => router.push('/')}
                    className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                    Return to Homepage
                </button>
            </div>
        );
    }

    return (
        <div className="p-4">
            <h1 className="text-2xl font-bold">Payment Successful!</h1>
            {productType === 'course' && <p>Redirecting you to your course...</p>}
            {/* {productType === 'ebook' && downloadUrl && (
                <>
                    <p>Your purchase was successful!</p>
                    <a href={downloadUrl} download className="mt-4 inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        Download Your Ebook
                    </a>
                </>
            )} */}
        </div>
    );
}

export default function PurchaseSuccessPage() {
    return (
        <Suspense fallback={
            <div className="p-4">
                <h1 className="text-2xl font-bold">Loading...</h1>
                <p>Please wait while we process your purchase.</p>
            </div>
        }>
            <PurchaseSuccessContent />
        </Suspense>
    );
}