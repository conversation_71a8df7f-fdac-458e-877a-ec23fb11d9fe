'use client';

import { useSession } from "next-auth/react";
import { useState } from "react";

export default function TestAuth() {
  const { data: session, status } = useSession();
  const [apiResult, setApiResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testApi = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/v0/products/recNWPBoKXsqggJ8M');
      const data = await response.json();
      setApiResult(data);
      if (!response.ok) {
        setError(`API error: ${data.error || response.status}`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Auth Test Page</h1>

      <div className="mb-6 p-4 bg-gray-100 rounded">
        <h2 className="text-xl font-semibold mb-2">Session Status: {status}</h2>
        {status === 'authenticated' ? (
          <div>
            <p>Logged in as: {session?.user?.email}</p>
            <p>User ID: {session?.user?.id}</p>
          </div>
        ) : status === 'loading' ? (
          <p>Loading session...</p>
        ) : (
          <p>Not authenticated</p>
        )}
      </div>

      <button
        onClick={testApi}
        disabled={loading || status !== 'authenticated'}
        className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
      >
        {loading ? 'Testing API...' : 'Test Product API'}
      </button>

      {error && (
        <div className="mt-4 p-4 bg-red-100 text-red-700 rounded">
          {error}
        </div>
      )}

      {apiResult && (
        <div className="mt-4">
          <h3 className="text-lg font-semibold mb-2">API Result:</h3>
          <pre className="bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(apiResult, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}