'use client';

import { useSession } from "next-auth/react";
import { Button } from '@/ui/index';
import { useState } from 'react';

export default function TestAirtable() {
    const { data: session, status } = useSession();
    const [result, setResult] = useState<string>('');
    const isLoading = status === "loading";

    // Test GET request
    const testGetEndpoint = async () => {
        try {
            const response = await fetch(`/api/v0/users/recOR3zp6sog2RMY2`);
            const data = await response.json();
            setResult(JSON.stringify(data, null, 2));
        } catch (error) {
            setResult(error instanceof Error ? error.message : 'Unknown error occurred');
        }
    };

    // Test POST request
    const handlePurchase = async () => {
        try {
            const response = await fetch(`/api/v0/users/recOR3zp6sog2RMY2/products/recNWPBoKXsqggJ8M`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const data = await response.json();
            setResult(JSON.stringify(data, null, 2));
        } catch (error) {
            setResult(error instanceof Error ? error.message : 'Unknown error occurred');
        }
    };

    // Test GET request
    const handleCheckPurchase = async () => {
        try {
            const response = await fetch(`/api/v0/users/recOR3zp6sog2RMY2/products/recNWPBoKXsqggJ8M`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const data = await response.json();
            setResult(JSON.stringify(data, null, 2));
        } catch (error) {
            setResult(error instanceof Error ? error.message : 'Unknown error occurred');
        }
    };

    const handleCheckPurchases = async () => {
        try {
            const response = await fetch(`/api/v0/users/recOR3zp6sog2RMY2/products`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const data = await response.json();
            setResult(JSON.stringify(data, null, 2));
        } catch (error) {
            setResult(error instanceof Error ? error.message : 'Unknown error occurred');
        }
    };

    const handleGetAllProducts = async () => {
        try {
            const response = await fetch(`/api/v0/products`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const data = await response.json();
            setResult(JSON.stringify(data, null, 2));
        } catch (error) {
            setResult(error instanceof Error ? error.message : 'Unknown error occurred');
        }
    };

    const handlegetDBproductById = async () => {
        try {
            const response = await fetch(`/api/v0/products/recNWPBoKXsqggJ8M`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const data = await response.json();
            setResult(JSON.stringify(data, null, 2));
        } catch (error) {
            console.error('POST Error:', error);
            setResult(error instanceof Error ? error.message : 'Unknown error occurred');
        }
    };

    if (isLoading) return <div>Loading...</div>;

    return (
        <div className="p-10">
            <pre>{JSON.stringify(session, null, 2)}</pre>
            <h1>Test Airtable</h1>
            <p>Session Status: {status}</p>
            <br />
            <br />
            <div>User: recOR3zp6sog2RMY2</div>
            <div className="flex gap-3 my-5">
                <Button style="dark" onClick={testGetEndpoint}>
                    Get user
                </Button>
                <Button style="dark" onClick={handlePurchase}>
                    Create Purchase
                </Button>

                <Button style="dark" onClick={handleCheckPurchases}>
                    Check Purchases
                </Button>
                <Button style="dark" onClick={handleCheckPurchase}>
                    Check specific product Purchases
                </Button>
            </div>

            <div>Product: recNWPBoKXsqggJ8M</div>

            <div className="flex gap-3 my-5">
                <Button style="dark" onClick={handleGetAllProducts}>
                    Get all
                </Button>
                <Button style="dark" onClick={handlegetDBproductById}>
                    Get by ID
                </Button>
            </div>
            <div className="mt-5">
                <h2>Result:</h2>
                <pre className="bg-gray-100 p-4 rounded mt-2">
                    {result || 'No result yet'}
                </pre>
            </div>
        </div>
    );
}
