// WARNING: This script attempts to remove common watermarking signals from AI-generated text.
// It does not guarantee complete removal and may affect text quality.

const VERBOSE = true; // Toggle to log detected watermarks

/**
 * Remove zero-width Unicode characters
 */
function removeZeroWidthChars(text) {
  const pattern = /[\u200B-\u200D\uFEFF]/g;
  const matches = text.match(pattern);
  if (VERBOSE && matches) {
    // console.log(`Zero-width characters found: ${matches.length}`);
  }
  return text.replace(pattern, '');
}

/**
 * Remove suspicious metadata or disclaimers
 */
function stripDisclaimers(text) {
  const disclaimerPatterns = [
    /This content was generated by an AI[^.]*\.?/gi,
    /Generated by: .*?\n?/gi,
    /This article was created with the help of AI\.?/gi,
  ];
  let cleaned = text;
  disclaimerPatterns.forEach(pattern => {
    const matches = cleaned.match(pattern);
    if (VERBOSE && matches) {
      // console.log(`Disclaimers found: ${matches.length} match(es)`);
    }
    cleaned = cleaned.replace(pattern, '');
  });
  return cleaned;
}

/**
 * Normalize synonyms and paraphrased phrases back to common forms
 */
function normalizeSynonyms(text) {
  const replacements = [
    [/in order to/g, 'to'],
    [/as a result of/g, 'because of'],
    [/due to the fact that/g, 'because'],
  ];
  let cleaned = text;
  replacements.forEach(([pattern, replacement]) => {
    const matches = cleaned.match(pattern);
    if (VERBOSE && matches) {
      // console.log(`Synonym pattern found: '${pattern}' - ${matches.length} match(es)`);
    }
    cleaned = cleaned.replace(pattern, replacement);
  });
  return cleaned;
}

/**
 * Heuristically remove stylometric patterns (e.g. repeated sentence structure)
 * Warning: Experimental and can affect quality
 */
function removeStylometricPatterns(text) {
  const sentences = text.match(/[^.!?]+[.!?]/g) || [];
  const repeated = sentences.filter((s, i, arr) => arr.indexOf(s) !== i);
  if (VERBOSE && repeated.length > 0) {
    // console.log(`Repeated stylometric sentence(s) found: ${repeated.length}`);
  }
  return sentences
    .map((sentence, i) => (i % 2 === 0 ? sentence : sentence.toLowerCase()))
    .join(' ');
}

/**
 * Master function to clean AI watermark artifacts from text
 */
export default function cleanWatermarkedText(text) {
  let cleaned = text;
  cleaned = removeZeroWidthChars(cleaned);
  cleaned = stripDisclaimers(cleaned);
  cleaned = normalizeSynonyms(cleaned);
  cleaned = removeStylometricPatterns(cleaned);
  return cleaned.trim();
}

// Example usage
// const input = `This content was generated by an AI. In order to improve results, the system was tuned.\u200B This sentence is overly uniform. This sentence is overly uniform. This sentence is overly uniform.`;
// const output = cleanWatermarkedText(input);
// console.log("\nCleaned Output:\n", output);
