// import { cache } from 'react';
// import { getUserPurchase } from '@/controllers/purchaseController';

// Server-side function with built-in caching
// export const getUserPurchaseStatus = cache(async (userId: string, productId: string) => {
//   if (!userId || !productId) {
//     return { hasPurchased: false };
//   }
  
//   try {
//     const purchases = await getUserPurchase(userId, productId);
//     return { 
//       hasPurchased: purchases.length > 0,
//       purchases
//     };
//   } catch (error) {
//     console.error('Error fetching purchase status:', error);
//     return { hasPurchased: false, error };
//   }
// });

// Client-side hook for components that need purchase status
// export async function getPurchaseStatus(userId: string, productId: string) {
//   if (!userId || !productId) {
//     return { hasPurchased: false };
//   }
  
//   try {
//     const response = await fetch(`/api/v0/users/${userId}/products/${productId}`);
//     if (!response.ok) {
//       throw new Error(`Error: ${response.status}`);
//     }
    
//     const data = await response.json();
//     return { 
//       hasPurchased: Object.keys(data).length > 0,
//       purchases: data
//     };
//   } catch (error) {
//     console.error('Error fetching purchase status:', error);
//     return { hasPurchased: false, error };
//   }
// }