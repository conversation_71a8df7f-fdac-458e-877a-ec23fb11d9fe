export class SendGridMailer {
    static async sendVerificationRequest({
        identifier: email,
        url,
        provider,
    }) {
        const { host } = new URL(url)

        const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${provider.auth.pass}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                personalizations: [
                    {
                        to: [{ email }],
                    },
                ],
                from: { email: provider.from },
                subject: `Sign in to ${host}`,
                content: [
                    {
                        type: 'text/html',
                        value: `<html><body><p>Please click the link below to sign in:</p><p><a href="${url}">Sign in</a></p></body></html>`,
                    },
                ],
            }),
        })

        if (!response.ok) {
            throw new Error('Failed to send verification email')
        }
    }
}