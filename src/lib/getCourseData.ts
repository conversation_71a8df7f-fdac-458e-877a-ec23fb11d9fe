// src/lib/getCourseData.ts
import { Course, Section, Lesson } from '@/types/course';
// import { Session } from '@/types/user';
import { Session as NextAuthSession } from 'next-auth'; 
import { getUserPurchaseStatus } from '@/controllers/purchaseController';
import { getDBproductBySlug } from '@/controllers/productController';

type CourseDataParams = {
  rawData: Course[];
  courseSlug?: string;
  sectionSlug?: string;
  lessonSlug?: string;
  hasPurchased?: boolean;
  session?: NextAuthSession;
};

type CourseDataResult = {
  courseData: Course;
  currentCourse?: Course;
  currentSection?: Section;
  currentLesson?: Lesson;
  nextLesson?: Lesson;
  nextFreeLesson?: Lesson;
  hasPurchased?: boolean;
  error: string | null;
  isLoading: boolean;
};

/**
 * Function to retrieve and process course data with flexible parameters
 * 
 * @param params Object containing the parameters
 * @returns Object containing the processed data and any errors
 */
export default async function getCourseData({
  rawData,
  courseSlug,
  sectionSlug,
  lessonSlug,
  session,

}: CourseDataParams): Promise<CourseDataResult> {
  // console.log('getCourseData', courseSlug, sectionSlug, lessonSlug, session, rawData);
  let hasPurchased = false
  const courseData: Course = {} as Course;
  try {
    if (session) {
      // Find the product ID from DATABASE.
      const product = await getDBproductBySlug(courseSlug);
      if (session?.user?.id && product?.id) {
          const result = await getUserPurchaseStatus(session.user.id, product.id);
          hasPurchased = result.hasPurchased;
      }
    }
    
    const result: CourseDataResult = {
      courseData,
      error: null,
      isLoading: false,
      hasPurchased
    };
    
    // Find the matching course we found in DB to the one in rawData

    if (courseSlug && Array.isArray(rawData)) {
      result.currentCourse = rawData.find(course => course.courseSlug === courseSlug);
      
      if (!result.currentCourse) {
        return {
          ...result,
          error: `Course not found: ${courseSlug}`
        };
      }
      
      // Find current section if sectionSlug is provided
      if (sectionSlug && result.currentCourse) {
        result.currentSection = result.currentCourse.section.find(
          section => section.sectionSlug === sectionSlug
        );
        
        if (!result.currentSection) {
          return {
            ...result,
            error: `Section not found: ${sectionSlug}`
          };
        }
        
        // Find current lesson if lessonSlug is provided
        if (lessonSlug && result.currentSection) {
          result.currentLesson = result.currentSection.lesson.find(
            lesson => { 
              return lesson.lessonSlug === lessonSlug}
          );
          
          if (!result.currentLesson) {
            return {
              ...result,
              error: `Lesson not found: ${lessonSlug}`
            };
          }
          
          // Find next lesson and next free lesson
          if (result.currentLesson) {
            const allLessons = getAllLessonsFlat(result.currentCourse);
            const currentIndex = allLessons.findIndex(lesson => 
              lesson.lessonSlug === result.currentLesson?.lessonSlug
            );
            
            // Next lesson is simply the next one in the array
            result.nextLesson = currentIndex >= 0 && currentIndex < allLessons.length - 1 
              ? allLessons[currentIndex + 1] 
              : undefined;
              
            // Next free lesson is the next unpaid one
            result.nextFreeLesson = allLessons.find((lesson, index) => 
              index > currentIndex && lesson.isPaid === false
            );
          }
        }
      }
    }
    
    return result;
  } catch (error) {
    console.error('Error in getCourseData:', error);
    return {
      courseData,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      isLoading: false
    };
  }
}

/**
 * Helper function to get all lessons from a course as a flat array
 */
function getAllLessonsFlat(course: Course): Lesson[] {
  const allLessons: Lesson[] = [];
  
  course.section.forEach(section => {
    section.lesson.forEach(lesson => {
      allLessons.push(lesson);
    });
  });
  
  // Sort by lesson number if available
  return allLessons.sort((a, b) => 
    (a.number || 0) - (b.number || 0)
  );
}
