'use client'
import NextImage from 'next/image';

interface CustomImageProps {
  width?: number;
  height?: number;
  alt?: string;
  priority?: boolean;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
  className?: string;
  style?: React.CSSProperties;
  src: string;
}

export default function Image({
  src,
  className,
  style,
  width = 500,
  height = 500,
  alt = "",
  priority = false,
  placeholder = 'empty',
  blurDataURL,
  loading,
  onLoad = () => { },
  onError = () => { },
}: CustomImageProps) {
  return (
    <NextImage
      src={src}
      width={width}
      height={height}
      alt={alt}
      loading={loading}
      priority={priority}
      placeholder={placeholder}
      blurDataURL={blurDataURL}
      className={className}
      style={style}
      onLoad={onLoad}
      onError={onError}
    />
  );
}
