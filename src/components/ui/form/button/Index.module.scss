// .button {
//   @apply px-7 py-3;
//   @apply inline-block cursor-pointer;
//   @apply border border-primary-300;
//   @apply text-base tracking-wider uppercase;
//   @apply hover:bg-primary-200 hover:text-primary-900;
// }

// .light {
//   composes: button;
//   @apply bg-white text-black;
//   @apply hover:bg-primary-600 hover:border-primary-600 hover:text-white;
// }

// .link {
//   composes: button;
//   @apply border-0;
//   @apply bg-transparent text-black;
//   @apply hover:underline hover:bg-transparent;
// }

// .dark {
//   composes: button;
//   @apply bg-primary text-white;
//   @apply border-primary;
//   @apply hover:bg-primary-600 hover:text-white hover:border-primary-600;
// }

.icon {
  position: relative;
  padding-left: 55px;

  &::before {
    position: absolute;
    width: 35px;
    height: 35px;
    margin: auto;
    top: 0;
    // right: 0;
    left: 10px;
    bottom: 0;
  }
}

.iconPlay {
  composes: icon;

  &::before {
    content: url(/img/ui/play.svg);
    opacity: 0.7;
  }

  &:hover {
    &::before {
      opacity: 1;
    }
  }
}