// src/ui/index.tsx
import React from 'react';

interface Props {
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
}

export const Container = ({ children, className, size = 'xl' }: Props) => {
  let widthContainer;
  switch (size) {
    case 'sm':
      widthContainer = 'max-w-xl';
      break;
    case 'md':
      widthContainer = 'max-w-3xl';
      break;
    case 'lg':
      widthContainer = 'max-w-5xl';
      break;
    case 'xl':
      widthContainer = 'max-w-7xl';
      break;
  }

  return (
    <div className={`container mx-auto px-4 ${widthContainer} ${className}`}>
      {children}
    </div>
  );
};

export const Row = ({ children, className }: Props) => {
  return (
    <div className={`flex flex-col xl:flex-row gap-10 ${className}`}>
      {children}
    </div>
  )
};

export function Column({ children, className }: Props) {
  return (
    <div
      className={`col w-full ${className}`}
    >
      {children}
    </div>
  );
}

export const Section = ({ children, className }: Props) => {
  return <section className={`section mb-10 sm:mb-20 ${className}`}>{children}</section>;
}
