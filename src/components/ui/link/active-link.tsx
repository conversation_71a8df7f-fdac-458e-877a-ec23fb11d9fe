"use client";

import { usePathname } from "next/navigation";
import { Link } from "@/ui/index";
import linkProps from "@/types/link"

const ActiveLink = ({
  children,
  activeClassName,
  className,
  href = "#",
  onClick,
  scroll = true,
  underline = true,
  ...props
}: linkProps) => {
  const pathname = usePathname();
  const _activeClassName = activeClassName || "text-primary-600";
  return (
    <>
      <Link
        href={href}
        onClick={onClick}
        className={`${className} ${pathname === href ? _activeClassName : ""
          } hover:text-primary-600`}
        scroll={scroll}
        underline={underline}
        {...props}
      >
        {children}
      </Link>
    </>
  );
};

export default ActiveLink;
