'use client';

import NextLink from 'next/link';
import { ReactNode } from 'react';

interface LinkProps {
  href?: string;
  children: ReactNode;
  className?: string;
  underline?: boolean;
  onClick?: () => void;
  locale?: string;
  scroll?: boolean;
}

const Link = ({ href = "#", children, className = '', underline = true, onClick, locale, scroll }: LinkProps) => {
  return (
    <NextLink
      locale={locale}
      scroll={scroll}
      href={href}
      onClick={onClick}
      className={`${underline ? 'hover:underline' : ''} ${className}`}
    >
      {children}
    </NextLink>
  );
};

export default Link;
