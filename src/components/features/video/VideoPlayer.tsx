'use client';

import { useState } from 'react';
import Modal from '@/components/ui/modal';
import { Button } from '@/ui/index';

interface VideoPlayerProps {
    video: string;
    thumbnailVideo?: string;
    poster?: string;
    className?: string;
    mode?: 'direct' | 'modal';
    showThumbnail?: boolean;
    autoPlay?: boolean;
    muted?: boolean;
    loop?: boolean;
    controls?: boolean;
}

export default function VideoPlayer({
    video,
    thumbnailVideo,
    poster,
    className = '',
    mode = 'direct',
    showThumbnail = true,
    autoPlay = false,
    muted = false,
    loop = false,
    controls = true,
}: VideoPlayerProps) {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isHovered, setIsHovered] = useState(false);

    // Direct video player
    if (mode === 'direct') {
        return (
            <video
                className={`w-full rounded-lg shadow-lg ${className}`}
                controls={controls}
                autoPlay={autoPlay}
                muted={muted}
                loop={loop}
                playsInline
                poster={poster}
            >
                <source src={video} type="video/mp4" />
                Your browser does not support the video tag.
            </video>
        );
    }

    // Modal video player with optional thumbnail
    return (
        <>
            {showThumbnail ? (
                // Thumbnail with play button
                <div
                    className={`relative rounded-lg overflow-hidden ${className}`}
                    onMouseEnter={() => setIsHovered(true)}
                    onMouseLeave={() => setIsHovered(false)}
                >
                    <video
                        className="w-full rounded-lg shadow-lg"
                        autoPlay
                        muted
                        loop
                        playsInline
                        poster={poster}
                    >
                        <source src={thumbnailVideo || video} type="video/mp4" />
                        Your browser does not support the video tag.
                    </video>

                    {/* Play button overlay */}
                    <div className={`absolute inset-0 flex items-center justify-center transition-opacity duration-300 ${isHovered ? 'opacity-100' : 'opacity-0'
                        }`}>
                        <Button
                            onClick={() => setIsModalOpen(true)}
                            style="dark"
                            size="md"
                        >
                            Play video
                        </Button>
                    </div>
                </div>
            ) : (
                // Just the play button
                <div className={`text-center ${className}`}>
                    <Button
                        onClick={() => setIsModalOpen(true)}
                        style="dark"
                        size="md"
                    >
                        Play video
                    </Button>
                </div>
            )}

            {/* Video Modal */}
            <Modal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
            >
                <div className="relative aspect-video bg-black rounded-lg">
                    <video
                        className="w-full h-full rounded-lg"
                        controls
                        autoPlay
                        playsInline
                    >
                        <source src={video} type="video/mp4" />
                        Your browser does not support the video tag.
                    </video>
                </div>
            </Modal>
        </>
    );
}
