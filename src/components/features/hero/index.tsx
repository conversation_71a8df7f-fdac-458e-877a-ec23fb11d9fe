import Image from "next/image"

export default function Hero({ backgroundImage }: { backgroundImage: string }) {

  // const [user, setUser] = useState(null);

  return (
    <section className="flex flex-col items-center justify-center relative min-h-[calc(100vh-40px)] w-full overflow-hidden">
      {/* Background image */}

      {/* Content */}
      <div className="relative z-10 flex flex-col items-center justify-center h-full px-6 ">
        <div className={`py-10 mb-24 flex flex-col justify-center items-center w-full h-fullrelative`}>
          <h1 className="text-9xl text-center leading-36 z-10">
            <span className="text-2xl handwriten block">Photography education</span>
            Tati <PERSON>
          </h1>
          {/* 
      {meta && <div className="mt-7 flex gap-3 items-center justify-center z-10">
        {meta.duration && <span className="border border-primary-400 px-2 py-1 text-primary-800">Duration: {meta.duration}</span>}
        {meta.lessons && <span className="border border-primary-400 px-2 py-1 text-primary-800">Lessons: <Link href="#lessons">{meta.lessons}</Link></span>}
        {meta.instructor && <span className="border border-primary-400 px-2 py-1 text-primary-800">Instructor: <Link href={"/instructor/" + slugify(meta.instructor)}>{meta.instructor}</Link></span>}
      </div>} */}

          {/* {backgroundImage && <div className="absolute inset-0 z-0">
            <img src={backgroundImage} alt="Background" className="w-full h-full object-cover z-0" />
          </div>} */}

        </div>
        {/* <PageTitle
          subtitle="Photography education"
        // backgroundImage={backgroundImage}
        >Tati Frank</PageTitle> */}
        {/* <h1 className="text-center">
          <span className="block text-6xl md:text-8xl lg:text-9xl font-serif text-white tracking-wide">Tati Frank</span>
          <span className="block text-4xl md:text-6xl lg:text-7xl text-white font-signature mt-[-1rem] md:mt-[-2rem] ml-auto w-fit">
            Education
          </span>
        </h1>

        <div className="absolute bottom-24 right-6 md:right-12 max-w-xs md:max-w-sm text-right">
          <h2 className="text-xs tracking-widest text-white mb-2 uppercase">EDUCATION FOR PHOTOGRAPHERS</h2>
          <p className="text-sm text-white mb-1">
            Helping photographers <em>build abundant businesses</em>
          </p>
          <p className="text-sm text-white">so they can make more and work less.</p>
        </div> */}
      </div>
      {backgroundImage && <Image src={backgroundImage} alt="Background" fill className="object-cover z-0" />}
    </section>
  )
}

