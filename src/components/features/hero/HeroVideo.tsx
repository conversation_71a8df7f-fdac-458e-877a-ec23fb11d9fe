"use client"
import React, { useState } from 'react';
import { Button } from '@/ui/index';


interface HeroVideoProps {
    videoSrc: string;
    thumbnailSrc: string;
    title: React.ReactNode;
    subtitle: string;
    description: string;
    ctaText?: string;
    ctaLink?: string;
    duration?: string | null;
    lessonCount?: number | null;
}

export default function HeroVideo({
    videoSrc,
    thumbnailSrc,
    title,
    subtitle,
    description,
    ctaText = "Start Learning",
    ctaLink = "#",
    duration,
    lessonCount
}: HeroVideoProps) {
    const [isPlaying, setIsPlaying] = useState(false);
    const [isLoaded] = useState(false);

    const handleVideoClick = () => {
        console.log('Thumbnail clicked, starting video');
        setIsPlaying(true);
    };

    // Ensure proper YouTube embed URL format and autoplay parameters
    const getEmbedUrl = (src: string) => {
        // If it's already an embed URL, just add parameters
        if (src.includes('youtube.com/embed')) {
            return `${src}${src.includes('?') ? '&' : '?'}autoplay=1&mute=0&controls=1&rel=0&modestbranding=1&playsinline=1`;
        }
        // Convert watch URL to embed URL if needed
        const videoId = src.match(/(?:v=)([^&]+)/)?.[1];
        if (videoId) {
            return `https://www.youtube.com/embed/${videoId}?autoplay=1&mute=0&controls=1&rel=0&modestbranding=1&playsinline=1`;
        }
        return src; // Return original if no match
    };

    const embedVideoSrc = getEmbedUrl(videoSrc);

    return (
        <section className="relative">
            <div className="flex flex-col xl:flex-row h-screen min-h-fit">
                {/* Left side with content */}
                <div className="w-full xl:w-2/6 bg-primary-100 flex items-center justify-center p-10 order-2 xl:order-1">
                    <div className="px-8 md:px-12 max-w-xl space-y-6">
                        <span className='handwriten text-2xl'>Tati Frank</span>
                        <h1 className="text-3xl md:text-5xl font-bold text-stone-800 leading-tight">
                            {title}
                        </h1>

                        <h2 className="text-2xl md:text-2xl font-light text-primary-900">
                            {subtitle}
                        </h2>

                        <div className='flex flex-col p-5 text-primary-600 bg-primary-150 border-l-8 border-primary-200'>
                            <ul className='list-disc list-inside'>
                                <li>Duration: {duration}</li>
                                <li>Lessons: {lessonCount?.toString()}</li>
                                <li>Feedback from Tati</li>
                            </ul>
                        </div>

                        <p className="text-base text-primary-900 leading-relaxed">
                            {description}
                        </p>
                        {/* <Button className="bg-stone-800 text-white px-8 py-3 rounded-md hover:bg-stone-700 transition-colors duration-300"> */}
                        <Button style="dark" className="" href={ctaLink}>
                            {ctaText}
                        </Button>
                    </div>
                </div>

                {/* Right side - Video container */}
                <div className="w-full xl:w-4/6 h-full relative flex items-center justify-center bg-black bg-cover bg-center order-1 xl:order-2"
                    style={{ backgroundImage: `url(${thumbnailSrc})` }}
                >
                    {!isPlaying ? (
                        <div
                            className="relative w-full h-full cursor-pointer group overflow-hidden"
                            onClick={handleVideoClick}
                        >



                            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                            <div className="absolute inset-0 flex items-center justify-center">
                                <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center transform transition-transform duration-300 group-hover:scale-110 shadow-xl">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="32"
                                        height="32"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="#000"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        className="ml-1"
                                    >
                                        <polygon points="5 3 19 12 5 21 5 3"></polygon>
                                    </svg>
                                </div>
                            </div>
                            {!isLoaded && (
                                <div className="absolute inset-0 flex items-center justify-center">
                                    <div className="w-12 h-12 border-4 border-white border-t-transparent rounded-full"></div>
                                </div>
                            )}
                        </div>
                    ) : (
                        <iframe
                            src={embedVideoSrc}
                            allowFullScreen
                            allow="autoplay; encrypted-media"
                            className="w-full h-full"
                            title="Video Player"
                            onLoad={() => console.log('Iframe loaded:', embedVideoSrc)}
                            onError={() => console.log('Iframe failed to load')}
                        />
                    )}
                </div>
            </div>
        </section>
    );
}