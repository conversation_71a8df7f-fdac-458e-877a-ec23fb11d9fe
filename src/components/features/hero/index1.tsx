import React from 'react';
import { Container } from '@/ui/index';

interface HeroProps {
    videoSrc: string;
    title: string;
    subtitle: string;
    description: string;
}

export default function Hero({ videoSrc, title, subtitle, description }: HeroProps) {
    return (
        <section className="relative overflow-hidden bg-gradient-to-b from-primary-50 to-white py-12 md:py-20">
            <Container>
                <div className="flex flex-col-reverse md:flex-row items-center justify-between gap-8 md:gap-12">
                    {/* Left side - Text content */}
                    <div className="w-full md:w-1/2 space-y-6 text-center md:text-left">
                        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-primary-700">
                            {title}
                        </h2>
                        <h3 className="text-xl md:text-2xl font-medium text-primary-500">
                            {subtitle}
                        </h3>
                        <p className="text-lg text-gray-700 max-w-xl">
                            {description}
                        </p>
                        <div className="pt-4">
                            <button className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300 transform hover:scale-105">
                                Get Started Today
                            </button>
                        </div>
                    </div>

                    {/* Right side - Video */}
                    <div className="w-full md:w-1/2 flex justify-center md:justify-end ">
                        <div className="relative w-full max-w-[300px] h-[500px] md:h-[600px] rounded-2xl overflow-hidden shadow-xl">
                            <video
                                className="absolute top-0 left-0 w-full h-full object-cover"
                                autoPlay
                                muted
                                loop
                                playsInline
                            >
                                <source src={videoSrc} type="video/mp4" />
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    </div>
                </div>
            </Container>
        </section>
    );
} 