'use client';
import { <PERSON><PERSON>, <PERSON>, Image } from "@/ui/index";
import { Course } from '@/types/course';

interface ProductPromoBannerProps {
    className?: string;
    product: Course;
}

export default function ProductPromoBanner({
    className = "",
    product
    // productId,
}: ProductPromoBannerProps) {

    const {
        courseName,
        blurb,
        type,
        courseSlug,
        price,
        priceOld,
        thumbnail,
    } = product;

    if (!product) {
        return null;
    }

    const PromoText = () => (
        <div className="w-full">
            <h2 className="text-2xl font-bold mb-2">{courseName}</h2>
            <p className="text-gray-600 text-decoration-none inline-block">{blurb}</p>
        </div>
    );

    const PromoPrice = () => (
        <div className="flex flex-col gap-2">
            <span className="flex gap-3 text-primary-600">
                <span className="text-xl font-bold">
                    {price}{process.env.NEXT_PUBLIC_CURRENCY_SYMBOL}
                </span>

                {priceOld && priceOld !== price && (
                    <span className="text-lg">
                        <del className="text-gray-400 mr-2">{priceOld}{process.env.NEXT_PUBLIC_CURRENCY_SYMBOL}</del>
                    </span>
                )}

            </span>
            <Button style="dark" size="sm" href={`/${type}/${courseSlug}`}>Claim Offer</Button>
        </div>
    );

    return (
        // <div className={`p-5 border border-primary-200 rounded w-full flex ${className}`}>
        //     <div className="flex flex-col lg:flex-row gap-5 w-full">
        //         <div className={`flex flex-row gap-5 ${thumbnail ? "xl:w-full" : "xl:w-5/6"}`}>
        //             {thumbnail && (
        //                 <div className="w-1/6">
        //                     <Image src={thumbnail} alt="Thumbnail" />
        //                 </div>
        //             )}
        //             <div className={thumbnail ? "w-5/6" : "w-full"}>
        //                 <PromoText />
        //             </div>
        //         </div>
        //         <div className={`${thumbnail ? "xl:w-3/6 min-w-[150px]" : "xl:w-1/6"} flex xl:justify-end justify-normal items-center`}>
        //             <PromoPrice />
        //         </div>
        //     </div>
        // </div>
        <Link href={`/${type}/${courseSlug}`} className="border border-primary-200 rounded w-full flex flex-col sm:flex-row ${className}">
            {thumbnail && (
                <div className="w-full sm:w-2/6">
                    <Image src={thumbnail} alt="Thumbnail" className="w-full h-full object-cover"/>
                </div>
            )}

            <div className="w-full object-cover sm:w-4/6 p-5">
                <PromoText />
            </div>
        </Link>


    );
}
