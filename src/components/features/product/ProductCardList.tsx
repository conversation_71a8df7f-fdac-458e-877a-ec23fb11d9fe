import ProductPromoBanner from './productPromoBanner';
import { Container } from '@/ui/index';
import { Course } from '@/types/course';

interface ProductCardListProps {
  products: Course[];
}

export default function ProductCardList({ products }: ProductCardListProps) {
  return (
    <>
      <Container className="mt-10 flex gap-3 flex-col lg:flex-row">
    {
      products.map(course => (

        <ProductPromoBanner
          product={course}
          key={course.name}
        />

      ))
    }
          </Container>
    </>
  );
}