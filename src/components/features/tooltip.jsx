import React, { useState, useRef } from 'react';

const Tooltip = ({ text, children }) => {
  const [visible, setVisible] = useState(false);
  const timer = useRef(null);

  const handleMouseEnter = () => {
    timer.current = setTimeout(() => {
      setVisible(true);
    }, 350); 
  };

  const handleMouseLeave = () => {
    clearTimeout(timer.current);
    setVisible(false);
  };

  return (
    <div
      className="relative inline-block"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}
      {visible && (
        <div className="absolute bottom-full left-0 mb-2
                        bg-gray-800 text-white text-sm px-2 py-1 rounded shadow-lg z-90 whitespace-nowrap
                        transition-opacity duration-200 opacity-100">
          {text}
        </div>
      )}
    </div>
  );
};

export default Tooltip;
