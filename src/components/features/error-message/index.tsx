import { But<PERSON> } from "@/ui/index";
import { ReactNode } from "react";

interface ErrorMessageProps {
  children: ReactNode;
  backlink?: Backlink[];
}

interface Backlink {
  href?: string;
  label: string;
  onClick?: () => void;
}

export default function ErrorMessage({
  children,
  backlink
}: ErrorMessageProps) {
  return (
    <div className="m-auto text-center p-20">
      <p className="text-2xl">
        {children}
      </p>
      <div className="mt-5 flex gap-3 justify-center items-center">
        {!backlink &&
          <Button href={"/"}>Visit Home</Button>
        }
        {backlink && backlink.map(button =>
          <Button key={`${button.href}-${button.label}`} href={button.href || "#"} onClick={button.onClick}>{button.label}</Button>
        )}
        or
        <Button href={"/contact"}>Contact us</Button>
      </div>
    </div>)
}
