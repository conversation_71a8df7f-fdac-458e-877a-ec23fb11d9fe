'use client'
import { ActiveLink } from "@/ui/index";
import {
  Course,
  Lesson
} from "@/types/course";
import Tooltip from "@/features/tooltip";


export default function LessonTableOfContents({
  course,
  className,
  linkscroll = true,
  showPreviewLabel = false,
  showPaidContent = true
}: {
  course: Course;
  className?: string;
  linkscroll?: boolean;
  showPreviewLabel?: boolean;
  showPaidContent?: boolean;
}) {
  const courseSection = course.section

  return (
    <div className={className}>
      {courseSection && courseSection.map(section => {

        return (

          <div
            key={section.sectionSlug}
            className={`block border mb-1 px-4 py-3
          ${section.type === "attachment" && "bg-primary-100"}
          ${section.type === "feedback" && "bg-primary-100"}
        `}>
            {/* <br />courseSection: <pre>{JSON.stringify(course[0], null, 2)}</pre> */}

            {/* courseSection.courseSlug: {courseSection.courseSlug} */}
            <div className="font-bold text-lg mb-2">
              {section.name}
            </div>
            <div className="">
              {section.description}
            </div>
            {section.lesson && (
              <NestedLessons
                key={`NestedLessons${section.sectionSlug}`}
                lessons={section.lesson}
                courseSlug={course.courseSlug}
                sectionSlug={section.sectionSlug}
                linkscroll={linkscroll}
                showPreviewLabel={showPreviewLabel}
                showPaidContent={showPaidContent}
              />
            )}
          </div>
        )

      })
      }


    </div>)
}

function NestedLessons({
  lessons,
  courseSlug,
  sectionSlug,
  linkscroll,
  showPreviewLabel,
  showPaidContent
}: {
  lessons: Lesson[];
  courseSlug?: string,
  sectionSlug?: string,
  linkscroll?: boolean;
  showPreviewLabel?: boolean;
  showPaidContent?: boolean;
}) {
  return (
    <div className="mt-4 space-y-2">
      {lessons.map((lesson) => {
        const isLesson = lesson.type === "lesson";
        const isPaid = lesson.isPaid === true;
        const lessonSlug = lesson.lessonSlug || lesson.name.toLowerCase().replace(/\s+/g, '-');
        const link = `/course/${courseSlug}/${sectionSlug}/${lessonSlug}`;

        const content = (
          <>
            {isLesson && lesson.number ? `Lesson ${formatLessonNumber(lesson.number)}: ` : ''}
            {lesson.type === "attachment" && "Download: "}
            {lesson.name}
          </>
        );

        return (
          <div className="flex w-full items-center" key={`linkW${lessonSlug}`}>
            {/* 
            When Free show link
            When Paid and showPaidContent show tooltip
            */}
            {(!isPaid || showPaidContent) ? (
              <ActiveLink
                key={`link${lesson.number}${lessonSlug}`}
                href={link}
                className="text-primary-600 block"
                activeClassName="underline rounded"
                scroll={linkscroll}
                underline={true}
              >
                {content}
              </ActiveLink>
            ) : (
              <span className="text-primary-600">
                <Tooltip text="Available after purchase">
                  {content}
                </Tooltip>
              </span>
            )}
            {!isPaid && showPreviewLabel && (
              <span className="text-primary-700 border rounded text-xs px-2 py-1 ml-3">
                Free
              </span>
            )}
          </div>
        );
      })}
    </div>
  );
}

const formatLessonNumber = (count: number): string =>
  count < 10 ? `0${count}` : `${count}`;