'use client';
import React from 'react';
import CoursePurchaseButton from '@/components/features/course/coursePurchaseButton';
import { Course } from '@/types/course'
import { Session, SessionUser } from '@/types/user'
import Image from '@/ui/image';
import { useSession } from 'next-auth/react';


interface CoursePaymentSumProps {
    courseId: string;
    userId: string;
    course: Course;
    price?: number;
    priceOld?: number;
    session?: Session;
}

export default function CoursePaymentSum({ course, price, priceOld }: CoursePaymentSumProps) {
    const session = useSession()
    return (
        <>
            <div id="Buy"></div>
            <div className='flex items-center justify-center min-h-screen text-center p-20  mt-11 relative'>
                <div className="z-10 p-10 bg-white shadow-lg">
                    <h1 className="text-3xl font-bold mb-5">Purchase &quot;{course.name}&quot;</h1>
                    <div className='flex flex-col p-5 text-primary-600 bg-primary-150 border-l-8 border-primary-200 text-left '>
                        <ul className='list-disc list-inside'>
                            <li>Duration: {course.duration}</li>
                            <li>Lessons: {course.lessonCount?.toString()}</li>
                            {course.features?.map((feature, index) => (
                                <li key={index}>{feature.title}</li>
                            ))}
                        </ul>
                    </div>
                    {/* <pre>{JSON.stringify(course, null, 2)}</pre> */}
                    <CoursePurchaseButton
                        className='mt-5'
                        session={session.data?.user as SessionUser}
                        courseSlug={course.courseSlug || ''}
                        // courseName={course.name}
                        courseId={course.id}
                        coursePrice={price}
                        coursePriceOld={priceOld} />
                </div>
                <Image className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent z-0 w-full h-full object-cover" src={course.thumbnail || ''} alt={course.name} />
            </div>
        </>
    );
}
