import React from 'react';
import { Container } from '@/ui/index';
import { Course } from '@/types/course';

interface CourseFeaturesProps {
    course: Course;
}

export default function CourseFeatures({ course }: CourseFeaturesProps) {
    const features = course.features || [];
    const totalFeatures = features.length + 2; // +2 for duration and lessons

    return (
        <section className="py-16 md:py-24 bg-primary-100">
            <span className="md:grid-cols-3 md:grid-cols-4"></span>
            <Container>
                <div className={`grid grid-cols-1 md:grid-cols-${totalFeatures} md:gap-10`}>
                    <CourseFeatureBlock
                        title={`${course.lessonCount?.toString() || '0'} lessons`}
                        description="Step-by-step topics to build your foundation"
                    />
                    <CourseFeatureBlock
                        title={course.duration || ""}
                        description="Watch at your own pace, whenever it works for you"
                    />
                    {features.map((item, index) => (
                        <CourseFeatureBlock
                            key={index}
                            title={item.title}
                            description={item.description}
                        />
                    ))}
                </div>
            </Container>
        </section>
    );
}


interface CourseFeatureBlockProps {
    title: string;
    description: string;
}

function CourseFeatureBlock({ title, description }: CourseFeatureBlockProps) {
    return (
        <div className="p-6">
            <h3 className="text-xl md:text-2xl font-bold mb-3 text-stone-800">
                {title}
            </h3>
            <p className="text-stone-600">{description}</p>
        </div>
    );
}