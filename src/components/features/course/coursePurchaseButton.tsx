// src/components/features/course/coursePurchaseButton.tsx
'use client';
import { SessionUser } from '@/types/user';
import { Course } from '@/types/course';

import { useState, useEffect } from 'react';
import { useSession } from "next-auth/react";
import { useRouter } from 'next/navigation';
import { Button } from '@/ui/index';

interface CoursePurchaseButtonProps {
    courseSlug: string;
    className?: string;
    coursePrice?: number;
    coursePriceOld?: number;
    courseId: string;
    session: SessionUser;
}

export default function CoursePurchaseButton({
    coursePrice,
    coursePriceOld,
    courseId,
    session,
    className,
}: CoursePurchaseButtonProps) {
    const router = useRouter();
    const [hasPurchased, setHasPurchased] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(true);

    const { data: clientSession, status } = useSession();
    const userId = clientSession?.user?.id || session;
    const isAuthenticated = status === "authenticated";

    useEffect(() => {
        const checkPurchaseStatus = async () => {
            if (!isAuthenticated || !userId) {
                setIsLoading(false);
                return;
            }

            try {
                setIsLoading(true);
                // const encodedUserId = encodeURIComponent(userId);
                const response = await fetch(`/api/v0/users/${userId}/products`);

                if (!response.ok) {
                    throw new Error(`Error: ${response.status}`);
                }

                const purchases = await response.json();
                const hasUserPurchased = purchases.some(
                    (product: Course) => product.id === courseId
                );

                setHasPurchased(hasUserPurchased);
            } catch (error) {
                console.error('Error checking purchase status:', error);
            } finally {
                setIsLoading(false);
            }
        };

        checkPurchaseStatus();
    }, [isAuthenticated, userId, courseId]);

    const handlePurchaseClick = () => {
        // router.push(`/checkout/${courseSlug}`);
        router.push(`/checkout/${courseId}`);

        // Path:
        // - /checkout/${courseId}
        // - /api/v0/checkout_sessions
        // - stripe
        //      - success_url: `${baseUrl}/course/purchase-success?session_id={CHECKOUT_SESSION_ID}&product_type=${productType}&product_id=${productId}`,
        //      - cancel_url: `${baseUrl}/course/${product.productSlug}?canceled=true`,
        // /api/v0/complete-purchase -> 
        // /course/purchase-success -> 
        // /course/${courseSlug}
    };

    return (
        <>
            {isLoading ? (
                <Button style="dark" className={`px-4 py-2 rounded ${className}`} disabled={true}>
                    Loading...
                </Button>
            ) : hasPurchased ? (
                <Button style="dark" className={`px-4 py-2 rounded ${className}`} href="#">
                    Already Purchased
                </Button>
            ) : (
                <div className={`flex gap-3 items-center justify-center ${className}`}>
                    <Button
                        onClick={handlePurchaseClick}
                        style="dark"
                        className="px-4 py-2 rounded"
                        aria-label={`Purchase course for ${(coursePrice).toFixed(2)} eur`}
                    >
                        {`Buy ${(coursePrice).toFixed(2)} ${process.env.NEXT_PUBLIC_CURRENCY_SYMBOL}`} 
                    </Button>
                    {
                        coursePriceOld && (
                            <div className='flex justify-center items-center h-full'>
                                <del>{coursePriceOld} {process.env.NEXT_PUBLIC_CURRENCY_SYMBOL}</del>
                            </div>
                        )
                    }

                </div>

            )}
        </>
    );
}
