'use client';
import { <PERSON><PERSON>, Image, Link } from '@/ui/index';
import CoursePurchaseButton from './coursePurchaseButton';
import { useSession } from 'next-auth/react';
import { Course, Lesson as LessonType } from '@/types/course';
import { SessionUser } from '@/types/user';

// const hasPurchasedCourse = true;

interface LessonProps {
    currentCourse: Course;
    currentLesson: LessonType;
    nextLesson: LessonType;
    nextFreeLesson: LessonType;
    hasPurchasedCourse: boolean;
}


export default function Lesson({ currentCourse, currentLesson, nextLesson, nextFreeLesson, hasPurchasedCourse }: LessonProps) {
    const session = useSession()

    return (
        <>
            {/* <pre>{JSON.stringify(data, null, 2)}</pre> */}

            {hasPurchasedCourse === undefined ? "Loading..." :
                <>
                    {(hasPurchasedCourse || !currentLesson.isPaid) ? (
                        <>
                            {currentLesson.video && (
                                <video
                                    controls
                                    src={currentLesson.video}
                                    className="w-full rounded-lg shadow-md"
                                >
                                    Your browser does not support the video tag.
                                </video>
                            )}
                            <div className='my-5 text-zinc-400 text-sm'>
                                <span className="capitalize">{currentLesson.type}</span> duration ~ {currentLesson.duration}
                            </div>
                            <p className="mt-5 text-gray-700">
                                {currentLesson.content}
                            </p>
                            {nextLesson ? (
                                <Button
                                    style="dark"
                                    className="mt-4"
                                    href={`/course/${nextLesson.courseSlug}/${nextLesson.sectionSlug}/${nextLesson.lessonSlug}`}
                                >
                                    Next: {nextLesson.name || "Next Lesson"}
                                </Button>
                            ) : (
                                <p className="mt-4 text-green-600">
                                    Congratulations! You have completed the course!
                                </p>
                            )}
                        </>
                    ) : (
                        <div className="mt-4 relative">
                            <div className=" absolute top-20 left-0 right-0 m-auto bg-white shadow-2xl rounded-lg z-10 h-fit w-fit p-10 flex flex-col items-center justify-center gap-5">
                                <span className='text-2xl'>
                                    You haven&apos;t purchased this course yet.
                                </span>

                                {/* <Button
                                    style="dark"
                                    className=""
                                    href={`/Buy}`}
                                >
                                    Buy course
                                </Button> */}

                                {/* {console.log(currentLesson)}
                                {console.log(currentCourse)} */}

                                <CoursePurchaseButton
                                    session={session.data?.user as SessionUser}
                                    courseSlug={currentLesson.courseSlug}
                                    courseName={currentLesson.courseName}
                                    courseId={currentCourse.id}
                                    coursePrice={currentCourse.price}
                                    coursePriceOld={currentCourse.priceOld} />

                                {nextFreeLesson && nextFreeLesson.lessonSlug && (
                                    <>

                                        <div className='w-1/2 text-center'>
                                            <span className='opacity-50'>Or check out the next free lesson: </span>
                                            <Link
                                                className="underline"
                                                href={`/course/${nextFreeLesson.courseSlug}/${nextFreeLesson.sectionSlug}/${nextFreeLesson.lessonSlug}`}
                                            >
                                                {nextFreeLesson.name || "Next Lesson"}
                                            </Link>
                                        </div>
                                    </>
                                )}

                            </div>
                            <div className='blur blur-xs article'>
                                {currentLesson.video && (
                                    <>
                                        <Image src={currentLesson.thumbnail} alt={currentLesson.name} className='w-full h-auto' />
                                    </>
                                )}
                                <p className='mt-5'>{currentLesson.description}</p>
                                <p>
                                    This lesson is part of our premium course content and is currently locked. Unlocking this lesson will give you access to in-depth explanations, exclusive resources, and valuable insights that are designed to help you reach your learning goals faster and more effectively.
                                </p>
                                <p>
                                    By purchasing the course, you’ll not only gain access to this lesson but also to a comprehensive learning experience tailored to your needs. Once you complete your purchase, this lesson will be fully unlocked and available for you to view at any time.
                                </p>
                                <p>
                                    Take the next step on your learning journey—get access today and unlock your full potential!
                                </p>
                            </div>
                        </div>
                    )}
                </>
            }
        </>
    );
}
