import { Link } from '@/ui/index'; // Assuming you have this from earlier
import {getDBproducts} from '@/controllers/productController';

export default async function Footer() {
    const productsDB = await getDBproducts();
    return (
        <>
            <span className='mt-20'></span>
            <footer className="bg-black text-white py-10 mt-auto">
                <div className="container mx-auto px-4">
                    <div className="flex flex-col md:flex-row justify-between items-start gap-8">
                        {/* Branding/Intro Section */}
                        <div className="flex-1">
                            <h3 className="text-2xl font-semibold mb-4"><PERSON><PERSON></h3>
                            <p className="text-gray-400 text-sm">
                                Master natural light photography, self-love through self-portraits, and essential camera skills with beginner-friendly courses.
                            </p>
                        </div>

                        {/* Course Links Section */}
                        <div className="flex-1">
                            <h4 className="text-lg font-medium mb-4">Explore Courses</h4>
                            <ul className="space-y-2 text-gray-300 text-sm">
                                {productsDB.map((product) => (
                                    <li key={product.id}>
                                        <Link href={`/${product.type}/${product.slug}`} className="hover:text-white transition-colors">
                                            {product.name}
                                        </Link>
                                    </li>
                                ))}
                            </ul>
                        </div>

                        {/* Contact/Social Section */}
                        <div className="flex-1">
                            <h4 className="text-lg font-medium mb-4">Get in Touch</h4>
                            <ul className="space-y-2 text-gray-300 text-sm">
                                {/* <li>Email: <a href="mailto:<EMAIL>" className="hover:text-white transition-colors"><EMAIL></a></li> */}
                                <li>
                                    {/* Follow: */}
                                    <a href="https://instagram.com/tatifrank" className="hover:text-white ml-1 transition-colors">Instagram</a>
                                    <a href="https://www.youtube.com/@TatiFrank" className="hover:text-white ml-1 transition-colors">YouTube</a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    {/* Bottom Bar */}
                    <div className="mt-8 pt-6 border-t border-gray-800 text-center text-gray-400 text-sm">
                        <p>&copy; {new Date().getFullYear()} Tati Frank Education. All rights reserved.</p>
                    </div>
                </div>
            </footer>
        </>
    );
}