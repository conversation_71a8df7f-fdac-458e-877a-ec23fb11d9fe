import React from 'react';
import { Container } from '@/ui/index';

export default function AbundancePlan() {
    return (
        <section className="py-16 md:py-24 bg-stone-50">
            <Container>
                <div className="text-center mb-14">
                    <h2 className="text-2xl md:text-3xl font-bold mb-4 text-stone-800">Your Learning Journey</h2>
                    <p className="text-lg text-stone-600 max-w-2xl mx-auto">
                        A thoughtfully designed path to help you develop your photography skills naturally.
                    </p>
                </div>

                <div className="relative">
                    {/* Vertical line connecting steps */}
                    <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-px bg-stone-300 hidden md:block"></div>

                    {/* Step 1 */}
                    <div className="md:flex items-center mb-16 relative">
                        <div className="md:w-1/2 md:pr-12 mb-6 md:mb-0 text-right">
                            <h3 className="text-xl md:text-2xl font-bold mb-3 text-stone-800">Foundation</h3>
                            <p className="text-stone-600">
                                Build a solid understanding of your camera and basic composition principles that will serve as the foundation for all your future work.
                            </p>
                        </div>
                        <div className="absolute left-1/2 transform -translate-x-1/2 w-10 h-10 rounded-full bg-white border-2 border-stone-300 z-10 hidden md:block"></div>
                        <div className="md:w-1/2 md:pl-12">
                            <div className="aspect-video bg-stone-100 rounded-md"></div>
                        </div>
                    </div>

                    {/* Step 2 */}
                    <div className="md:flex items-center mb-16 relative flex-row-reverse">
                        <div className="md:w-1/2 md:pl-12 mb-6 md:mb-0 text-left">
                            <h3 className="text-xl md:text-2xl font-bold mb-3 text-stone-800">Natural Light</h3>
                            <p className="text-stone-600">
                                Discover how to work with available light to create mood and atmosphere in your photographs without complicated equipment.
                            </p>
                        </div>
                        <div className="absolute left-1/2 transform -translate-x-1/2 w-10 h-10 rounded-full bg-white border-2 border-stone-300 z-10 hidden md:block"></div>
                        <div className="md:w-1/2 md:pr-12">
                            <div className="aspect-video bg-stone-100 rounded-md"></div>
                        </div>
                    </div>

                    {/* Step 3 */}
                    <div className="md:flex items-center relative">
                        <div className="md:w-1/2 md:pr-12 mb-6 md:mb-0 text-right">
                            <h3 className="text-xl md:text-2xl font-bold mb-3 text-stone-800">Personal Style</h3>
                            <p className="text-stone-600">
                                Develop your unique voice and vision through thoughtful assignments and guided exploration of your creative interests.
                            </p>
                        </div>
                        <div className="absolute left-1/2 transform -translate-x-1/2 w-10 h-10 rounded-full bg-white border-2 border-stone-300 z-10 hidden md:block"></div>
                        <div className="md:w-1/2 md:pl-12">
                            <div className="aspect-video bg-stone-100 rounded-md"></div>
                        </div>
                    </div>
                </div>
            </Container>
        </section>
    );
} 