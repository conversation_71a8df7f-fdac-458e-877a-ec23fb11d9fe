import { Link, Image } from "@/ui/index";
import slugify from "@/utils/slugify";

interface PageTitleProps {
  children: React.ReactNode;
  subtitle?: string;
  backgroundImage?: string;
  meta?: {
    duration?: string;
    lessons?: string;
    instructor?: string;
  };
  className?: string;
}

export default function PageTitle({
  children,
  subtitle,
  className = "",
  backgroundImage,
  meta,
}: PageTitleProps) {

  return (
    <div className={`py-10 flex flex-col justify-center items-center w-full h-full ${className} relative`}>
      <h1 className="text-6xl text-center leading-16 z-10">
        {subtitle && <span className="text-lg handwriten block">{subtitle}</span>}
        {children}
      </h1>

      {meta && <div className="mt-7 flex gap-3 items-center justify-center z-10">
        {meta.duration && <span className="border border-primary-400 px-2 py-1 text-primary-800">Duration: {meta.duration}</span>}
        {meta.lessons && <span className="border border-primary-400 px-2 py-1 text-primary-800">Lessons: <Link href="#lessons">{meta.lessons}</Link></span>}
        {meta.instructor && <span className="border border-primary-400 px-2 py-1 text-primary-800">Instructor: <Link href={"/instructor/" + slugify(meta.instructor)}>{meta.instructor}</Link></span>}
      </div>}

      {backgroundImage && <div className="absolute inset-0 z-0">
        <Image src={backgroundImage} alt="Background" className="w-full h-full object-cover z-0" />
      </div>}

    </div>
  );
}


