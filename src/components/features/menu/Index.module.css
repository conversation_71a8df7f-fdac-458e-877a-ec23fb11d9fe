.header {
  /* position: -webkit-sticky;  */
  /* position: sticky; */
  top: 0;
}

.menu {
  /* @apply justify-center gap-4 hidden md:flex z-40 items-center text-sm lg:text-base; */
}

.menuItem {
  /* @apply block lg:inline-block lg:mt-0 hover:text-primary-900 hover:underline; */
}

.menuToggle {
  /* @apply fixed md:hidden items-center px-3 py-2 rounded hover:bg-white z-50 right-3 top-5 bg-white shadow-md; */
}

.menuToggleClose {
  /* @apply md:hidden flex items-center px-3 py-2 rounded hover:bg-white z-50 fixed right-3 top-5 bg-white; */
}

@media (max-width: 1024px) {
  .menuVisible {
    /* @apply flex flex-col fixed inset-0 bg-primary-200 text-center text-xl overflow-scroll; */
  }
}