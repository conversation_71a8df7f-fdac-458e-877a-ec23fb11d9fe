// src/components/features/menu/index.tsx
"use client";
import { useState } from "react";
import { signOut, signIn, useSession } from "next-auth/react";
import { Container, Link } from "@/ui/index";
import LinkProps from "@/types/link";
import styles from "./Index.module.css";

interface MenuItem {
  name: string;
  href: string;
  highlight?: boolean;
}

interface TopLevelMenuProps {
  className?: string;
  logo?: string;
  menu?: boolean;
  data?: MenuItem[];
}

export default function TopLevelMenu({
  logo,
  menu = true,
  data = [],
  className
}: TopLevelMenuProps) {
  const [menuOpen, setMenuOpen] = useState(false);
  const { status } = useSession();
  const isAuthenticated = status === "authenticated";

  function toggleMenu() {
    setMenuOpen(!menuOpen);
  }

  if (!menu) return null;

  return (
    <nav className={`${styles.header}
      md:bg-primary-100
      py-0
      md:py-2
      flex
      items-center
      justify-center
      flex-wrap
      p-6
      md:sticky
      absolute
      z-50
      ${className || ""}
    `}>
      <Container className="flex justify-center items-center">
        {logo && (
          <div className="text-2xl md:text-xl">
            TatiFrank
          </div>
        )}

        {/* Hamburger button for mobile */}
        <button
          onClick={toggleMenu}
          className="md:hidden flex items-center px-3 py-2 rounded hover:bg-white z-40 bg-white shadow-md
          fixed right-4 top-[16px] p-2 w-[40px] h-[40px]
          "
          aria-label={menuOpen ? "Close menu" : "Open menu"}
          aria-expanded={menuOpen}
        >
          <svg
            className="h-4 w-4"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>Menu</title>
            <path d="M0 3h20v2H0V3zm0 6h20v2H0V9zm0 6h20v2H0v-2z" />
          </svg>
        </button>

        {/* Menu content */}
        <div
          className={`
            md:flex md:items-center md:w-auto
            ${menuOpen ? "block fixed inset-0 bg-primary-200 pt-16 px-4 z-40" : "hidden"}
          `}
        >
          {menuOpen && (
            <button
              onClick={toggleMenu}
              className="md:hidden absolute right-4 top-4 p-2 rounded bg-white"
              aria-label="Close menu"
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </button>
          )}

          <MenuLinks
            data={data}
            // onItemClick={toggleMenu}
            isAuthenticated={isAuthenticated}
            isMobile={menuOpen}
          />
        </div>
      </Container>
    </nav>
  );
}

interface MenuLinksProps {
  data: MenuItem[];
  onItemClick?: () => void;
  isAuthenticated: boolean;
  isMobile?: boolean;
}

function MenuLinks({ data, onItemClick = () => { }, isAuthenticated, isMobile }: MenuLinksProps) {
  return (
    <div className={`${isMobile ? "flex flex-col items-center space-y-6 text-xl" : "flex flex-row items-center gap-4"}`}>
      {data?.map((item, index) => (
        <MenuLink
          href={item.href}
          highlight={item.highlight}
          key={`toplevelmenu_${index}`}
          onClick={onItemClick}
          underline={false}
        >
          {item.name}
        </MenuLink>
      ))}

      {!isAuthenticated ? (
        <Link
          onClick={() => signIn()}
          underline={false}
          className=""
        >
          Sign in
        </Link>
      ) : (
        <Link
          onClick={() => signOut()}
          underline={false}
          className=""
        >
          Sign out
        </Link>
      )}
    </div>
  );
}

function MenuLink({
  locale,
  href,
  children,
  highlight,
  underline = false,
  onClick
}: LinkProps) {
  return (
    <Link
      locale={locale}
      onClick={onClick}
      href={href}
      className={`${highlight ? "bg-primary-200 py-1 px-3" : ""}`}
      underline={underline}
    >
      {children}
    </Link>
  );
}
