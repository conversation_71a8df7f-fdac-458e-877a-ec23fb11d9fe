import React from 'react';
import { Container } from '@/ui/index';
import Image from 'next/image';

const testimonials = [
    {
        id: 1,
        quote: '"If you\'re new here, do the work, participate. This has been the BEST community I have ever been in."',
        name: "JEANNEMARIE VIGGIANO",
        image: "/images/testimonial-1.jpg",
    },
    {
        id: 2,
        quote: '"Cheers to confidently raising prices and brand elevation! Today, I\'m booking couples at triple the price of my booking rate last year."',
        name: "WHITNEY YANG",
        image: "/images/testimonial-2.jpg",
    },
    {
        id: 3,
        quote: '"This is an incredible investment in your business and personal growth and 100% worth the money spent on it."',
        name: "JUDITH RAE",
        image: "/images/testimonial-3.jpg",
    },
];

export default function Testimonials() {
    return (
        <section className="py-16 md:py-24 bg-primary-100">
            <Container>
                <div className="text-center mb-14">
                    <h2 className="text-2xl md:text-3xl font-bold mb-4 text-stone-800">Student Success Stories</h2>
                    <p className="text-lg text-stone-600 max-w-2xl mx-auto">
                        See how our students are transforming their photography skills and careers.
                    </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    {testimonials.map((testimonial) => (
                        <div key={testimonial.id} className="bg-white p-8 flex flex-col items-center text-center shadow-sm border border-stone-100 rounded-md hover:shadow-md transition-shadow">
                            <div className="w-20 h-20 rounded-full overflow-hidden relative mb-6">
                                <Image
                                    src={testimonial.image || "/images/placeholder.jpg"}
                                    alt={`${testimonial.name} profile`}
                                    fill
                                    className="object-cover"
                                />
                            </div>

                            <blockquote className="text-lg text-stone-800 mb-6">{testimonial.quote}</blockquote>

                            <cite className="text-xs uppercase tracking-widest text-stone-600 not-italic font-medium">{testimonial.name}</cite>
                        </div>
                    ))}
                </div>
            </Container>
        </section>
    );
} 