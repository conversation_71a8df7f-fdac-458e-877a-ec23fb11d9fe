import Image from "next/image"
import Link from "next/link"

export default function AbundancePlan() {
  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="max-w-[1400px] mx-auto px-6">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
          {/* Left image */}
          <div className="lg:col-span-4 relative">
            <Image
              src="/backstage/DSC06418s.jpg"
              alt="Model in wedding dress"
              width={500}
              height={700}
              className="w-full h-auto"
            />
          </div>

          {/* Center content */}
          <div className="lg:col-span-4 text-center lg:text-left">
            <h2 className="section-title mb-6">Do what you love, love what you do</h2>
            <p className="text-lg font-serif italic text-[#666666] mb-8">
              Master natural light photography, self-love through self-portraits, and essential camera skills with beginner-friendly courses.
            </p>
            {/* <p className="text-[#666666] mb-8">
              The course for photographers who dream of a career that allows them to work with the best, book top-tier
              clients, and enjoy a thriving business for the long-term.
            </p> */}
            <Link href="/abundance-plan" className="inline-block btn-primary">
              LEARN MORE
            </Link>
          </div>

          {/* Right images */}
          <div className="lg:col-span-4 grid grid-cols-1 gap-6">
            <div className="relative h-64">
              <Image
                src="/portfolio/5.jpg"
                alt="Bride in wedding dress"
                fill
                className="object-contain"
              />
            </div>
            <div className="relative h-64 w-full -ml-10">
              <Image
                src="/portfolio/6.jpg"
                alt="Bride in garden"
                fill
                className="object-contain"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

