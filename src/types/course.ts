export interface Course {
    name: string;
    id: string;
    courseName: string;
    price: number;
    priceOld?: number;
    type: string;
    slug: string;
    courseSlug?: string;
    blurb: string;
    description: string;
    thumbnail?: string;
    video?: string;
    attachment?: Attachment;
    duration?: string;
    lessonCount?: number;
    instructor?: string;
    categories?: string[];
    content?: string;
    features?: Feature[];
    section: Section[];
}

export interface Attachment {
    name: string;
    description: string;
    size: string;
    type: string;
    url: string;
    version: string;
}

export interface Section {
    name: string;
    description?: string;
    content?: string;
    sectionSlug?: string;
    lesson: Lesson[];
    thumbnail?: string;
    link?: string;
    type?: string;
    video?: string;
    duration?: number,
}

export interface Lesson {
    name: string;
    courseSlug?: string;
    courseName?: string;
    sectionSlug?: string;
    lessonSlug?: string;
    video?: string;
    description?: string;
    content?: string;
    duration?: number,
    thumbnail?: string;
    type: string;
    isPaid: boolean;
    number?: number,
}

export interface Feature {
    title: string;
    description: string;
}
