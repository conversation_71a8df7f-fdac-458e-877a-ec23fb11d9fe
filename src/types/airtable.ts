export interface AirtableProduct {
    id: string;
    name: string;
    price: number;
    priceOld: number;
    slug: string;
    type: 'course' | string; // Replace with union of known types if needed
  }

export interface AirtableUser {
  email: string;
  emailVerified?: string | null;
  name?: string | null;
  image?: string | null;
}

export interface AirtableVerificationTokenRecord {
  fields: {
    identifier: string;
    token: string;
    expires: string | Date;
  };
}
// export interface AirtableVerificationTokenFields {
//   identifier: string;
//   token: string;
//   expires: string | Date;
// }
  