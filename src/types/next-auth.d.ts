import { DefaultSession, DefaultUser } from "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      email: string
      airtableId?: string
      customId?: string
    } & DefaultSession["user"]
  }
  
  interface User extends DefaultUser {
    airtableId?: string
    customId?: string
  }
}

declare module "@auth/core/adapters" {
  interface AdapterUser {
    airtableId?: string
    customId?: string
  }
}
