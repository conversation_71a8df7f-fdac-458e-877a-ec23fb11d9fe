// src/types/user.ts

export interface User {
    id: string;
    email: string;
    name: string | null;
    emailVerified?: Date | null;
    image?: string | null;
    customId?: string;
    airtableId?: string;
}

export interface SessionUser {
  id: string;
  airtableId: string;
  customId: string;
  email: string;
  emailVerified: string; // or `Date`
};

export interface Session {
    id: string;
    sessionToken: string;
    userId: string;
    expires: string; // or `Date` if you parse it
    user: SessionUser;
    status: string;
  }
