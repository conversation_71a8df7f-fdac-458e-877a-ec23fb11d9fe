{"compilerOptions": {"target": "ES2017", "noImplicitAny": false, "strict": false, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["src/components/*"], "@/features/*": ["src/components/features/*"], "@/utils/*": ["./src/app/utils/*"], "@/user/*": ["./src/app/(user)/*"], "@/ui/*": ["./src/components/ui/*"], "@/api/*": ["./src/pages/api/*"], "@/hooks/*": ["./src/hooks/*"], "@/data/*": ["./src/data/*"], "@/fpixel": ["./src/lib/fpixel.js"], "@/app/*": ["./src/app/*"], "@/app/features/*": ["./src/components/features/*"], "@/app/api/*": ["./src/app/pages/api/*"], "@/app/hooks/*": ["./src/app/hooks/*"], "@/app/fpixel": ["./src/app/lib/fpixel.js"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}