# getCourseData.ts Guide

## Overview

`getCourseData.ts` is a core utility function that handles course data retrieval and processing throughout the application. It provides a unified way to access course content while respecting purchase status.

## Function Signature

```typescript
export default async function getCourseData({
  rawData,
  courseSlug,
  sectionSlug,
  lessonSlug,
  session,
}: CourseDataParams): CourseDataResult
```

## Parameters

- `rawData`: Array of Course objects containing all course content
- `courseSlug`: Optional slug to filter for a specific course
- `sectionSlug`: Optional slug to filter for a specific section
- `lessonSlug`: Optional slug to filter for a specific lesson
- `session`: User session object for authentication status

## Return Value (CourseDataResult)

- `courseData`: Processed course data
- `currentCourse`: The requested course (if found)
- `currentSection`: The requested section (if found)
- `currentLesson`: The requested lesson (if found)
- `nextLesson`: The next lesson in sequence (if available)
- `nextFreeLesson`: The next free lesson in sequence (if available)
- `hasPurchased`: <PERSON><PERSON><PERSON> indicating if user has purchased the course
- `error`: Error message (if any)
- `isLoading`: Loading state indicator

## Key Features

1. **Purchase Status Verification**:
   - Checks if user has purchased the course via `getUserPurchaseStatus`
   - Uses product ID from database via `getDBproductBySlug`

2. **Content Navigation**:
   - Finds current course, section, and lesson based on provided slugs
   - Calculates next lesson and next free lesson for navigation

3. **Error Handling**:
   - Returns appropriate error messages when content is not found
   - Gracefully handles exceptions with detailed error reporting

## Related Functions

- `getDBproductBySlug`: Retrieves product information from the database using the course slug
- `getUserPurchaseStatus`: Checks if a user has purchased a specific product
- `getAllLessonsFlat`: Helper function that flattens the course structure for easier navigation
- `getProduct`: Combines database and content data, checks if the logged-in user has purchased the product, and returns either complete or limited content accordingly
- `getProductBySlug`: Similar to `getProduct`, retrieves product by slug and can modify returned data based on user's purchase status

## Usage Examples

1. **Course Page**:
   ```typescript
   // In /course/[courseSlug]/page.tsx
   const { currentCourse, hasPurchased } = await getCourseData({
     rawData: rawDataCourseContent,
     courseSlug: courseSlug,
     session: session
   });
   ```

2. **Lesson Page**:
   ```typescript
   // In /course/[courseSlug]/[sectionSlug]/[lessonSlug]/page.tsx
   const {
     currentCourse,
     currentLesson,
     nextLesson,
     nextFreeLesson,
     hasPurchased,
   } = await getCourseData({
     rawData: rawDataCourseContent,
     courseSlug: courseSlug,
     sectionSlug: sectionSlug,
     lessonSlug: lessonSlug,
     session: session
   });
   ```

This function serves as the central data access point for course content throughout the application, ensuring consistent data handling and access control.
