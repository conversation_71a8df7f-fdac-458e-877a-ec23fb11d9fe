# Payment Flow Guide

## Overview of Payment Flow

1. **Product Page** → **Checkout** → **Stripe** → **Success Page** → **Course Access**

## Key Components and Functions

### 1. Product Page (`/course/[courseSlug]/page.tsx`)
- Displays course details and purchase button
- Uses `<CoursePaymentSum>` component with course details and price
- Utilizes `getCourseData.ts` to retrieve and process course information

### 2. Purchase Button (`coursePurchaseButton.tsx`)
- Redirects to `/checkout/${courseId}`
- Initiates payment process

### 3. Checkout Page (`/checkout/[productId]/page.tsx`)
- Verifies user authentication
- Fetches product details
- Creates Stripe checkout session via API
- Redirects to Stripe payment page

### 4. Stripe Payment
- Processes payment
- Redirects to success URL with session ID

### 5. Success Page (`/course/purchase-success/page.tsx`)
- Verifies payment with `/api/v0/complete-purchase`
- Uses `getProduct(productId)` to fetch course details
- Redirects to first lesson

### 6. Course/Lesson Pages
- Use `getCourseData.ts` to retrieve course, section, and lesson data
- Apply access control based on purchase status
- Present navigation options using processed course data

## Product Retrieval Functions

The application uses several related functions to retrieve product information:

- `getProduct(productId)`: Main function that combines DB and content data. Checks if the logged-in user has purchased the product and returns either complete or limited content accordingly.
- `getProductBySlug(productSlug)`: Retrieves product by slug from the database. Similar to `getProduct`, it can check purchase status and modify returned data based on access rights.
- `getDBproductById(productId)`: Gets product from database by ID
- `getDBproductBySlug(productSlug)`: Gets product from database by slug

## `getProduct.ts` Usage

The function combines database product info with content data and provides different outputs based on purchase status:
- For users who have purchased: Returns complete product content with all lessons and materials
- For non-purchasers: Returns filtered content via `hidePaidContentInLessons()` which removes paid content (nullifies video and content fields in paid lessons)

```typescript
// src/app/actions/getProduct.ts
export async function getProduct(productId: string) {
    // Fetches product from DB and JSON data
    // Checks if user has purchased the product
    // Returns full content for purchasers
    // Returns limited content for non-purchasers via hidePaidContentInLessons()
}
```

Used in:
- Purchase success page to get course details
- Course/lesson pages to determine content access
- API endpoints to verify product existence

## Course Data Processing

### `src/lib/getCourseData.ts`

This core utility function handles course data retrieval and processing throughout the application:

- Retrieves course data based on provided slugs (course, section, lesson)
- Checks user purchase status via database integration
- Provides navigation helpers (next lesson, next free lesson)
- Handles error cases gracefully

Used in:
- Course page to display course overview and check purchase status
- Lesson pages to display lesson content and navigation options
- Success page flow to direct users to appropriate content after purchase

`getCourseData.ts` serves as the central access point for course content, ensuring consistent data handling and access control based on purchase status. It works alongside the product retrieval functions to create a complete content access system.

